import { Head } from "@inertiajs/react";
import AppLayout from "@/layouts/app/app-sidebar-layout";
import { Card } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Download, Filter } from "lucide-react";
import { Suspense, lazy, useState } from "react";
import { serviceStats, caseStats, dashboardData } from "./tempData/data";
import { type BreadcrumbItem } from "@/types";

// Lazy load ApexCharts components
const Chart = lazy(() => import('react-apexcharts'));

// Loading placeholder for charts
const ChartPlaceholder = ({ icon: Icon }: { icon: any }) => (
  <div className="h-[300px] flex items-center justify-center text-gray-400">
    <Icon className="h-12 w-12" />
  </div>
);

export default function Reports() {
  const [dateRange, setDateRange] = useState("last30");
  const [reportType, setReportType] = useState("summary");

  const breadcrumbs: BreadcrumbItem[] = [
    { title: "Dashboard", href: "/admin/dashboard" },
    { title: "Reports & Analytics", href: "/admin/reports" },
  ];

  // Service Distribution Chart Options
  const serviceDistributionOptions = {
    chart: {
      type: 'pie' as const,
      height: 300,
    },
    labels: serviceStats.serviceTypes.map(st => st.name),
    responsive: [{
      breakpoint: 480,
      options: {
        chart: {
          width: 200
        },
        legend: {
          position: 'bottom'
        }
      }
    }]
  };

  const serviceDistributionSeries = serviceStats.serviceTypes.map(st => st.count);

  // Monthly Applications Chart Options
  const monthlyApplicationsOptions = {
    chart: {
      type: 'line' as const,
      height: 300,
    },
    xaxis: {
      categories: serviceStats.monthlyApplications.map(ma => ma.month),
    },
    stroke: {
      curve: 'smooth' as const,
    }
  };

  const monthlyApplicationsSeries = [{
    name: 'Applications',
    data: serviceStats.monthlyApplications.map(ma => ma.count)
  }];

  // Case Types Chart Options
  const caseTypesOptions = {
    chart: {
      type: 'bar' as const,
      height: 300,
    },
    xaxis: {
      categories: caseStats.caseTypes.map(ct => ct.type),
    },
    plotOptions: {
      bar: {
        horizontal: false,
        columnWidth: '55%',
      },
    }
  };

  const caseTypesSeries = [{
    name: 'Cases',
    data: caseStats.caseTypes.map(ct => ct.count)
  }];

  // Resolution Time Chart Options
  const resolutionTimeOptions = {
    chart: {
      type: 'bar' as const,
      height: 300,
    },
    xaxis: {
      categories: caseStats.resolutionTimes.map(rt => rt.range),
    },
    plotOptions: {
      bar: {
        horizontal: true,
      },
    }
  };

  const resolutionTimeSeries = [{
    name: 'Cases',
    data: caseStats.resolutionTimes.map(rt => rt.count)
  }];

  // Performance Metrics Chart Options
  const performanceMetricsOptions = {
    chart: {
      type: 'area' as const,
      height: 300,
      toolbar: {
        show: true
      }
    },
    dataLabels: {
      enabled: false
    },
    stroke: {
      curve: 'smooth' as const,
      width: 2
    },
    xaxis: {
      categories: ['Week 1', 'Week 2', 'Week 3', 'Week 4']
    },
    yaxis: {
      title: {
        text: 'Count'
      }
    },
    tooltip: {
      shared: true
    }
  };

  const performanceMetricsSeries = [
    {
      name: 'Resolved Cases',
      data: [
        Math.round(dashboardData.resolvedToday * 0.7),
        Math.round(dashboardData.resolvedToday * 0.9),
        dashboardData.resolvedToday,
        Math.round(dashboardData.resolvedToday * 1.2)
      ]
    },
    {
      name: 'New Applications',
      data: [
        Math.round(dashboardData.pendingApplications * 0.8),
        dashboardData.pendingApplications,
        Math.round(dashboardData.pendingApplications * 1.1),
        Math.round(dashboardData.pendingApplications * 0.9)
      ]
    },
    {
      name: 'Verifications',
      data: [
        Math.round(dashboardData.pendingVerifications * 0.9),
        dashboardData.pendingVerifications,
        Math.round(dashboardData.pendingVerifications * 1.2),
        Math.round(dashboardData.pendingVerifications * 1.1)
      ]
    }
  ];

  // Application Processing Time Chart
  const processingTimeOptions = {
    chart: {
      type: 'bar' as const,
      height: 300,
      stacked: true,
      toolbar: {
        show: true
      }
    },
    plotOptions: {
      bar: {
        horizontal: false,
        columnWidth: '55%',
      },
    },
    xaxis: {
      categories: ['< 1 day', '1-2 days', '2-3 days', '3-5 days', '> 5 days'],
    },
    yaxis: {
      title: {
        text: 'Applications'
      }
    },
    legend: {
      position: 'top' as const,
      horizontalAlign: 'left' as const
    },
    fill: {
      opacity: 1
    }
  };

  const processingTimeSeries = [
    {
      name: 'High Priority',
      data: [8, 12, 5, 3, 1]
    },
    {
      name: 'Medium Priority',
      data: [5, 10, 8, 6, 4]
    },
    {
      name: 'Low Priority',
      data: [3, 6, 9, 8, 7]
    }
  ];

  return (
    <AppLayout breadcrumbs={breadcrumbs}>
      <Head title="Reports" />

      <div className="container py-6">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold">Reports & Analytics</h1>
          <div className="flex gap-4">
            <Select value={dateRange} onValueChange={setDateRange}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Select date range" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="last7">Last 7 days</SelectItem>
                <SelectItem value="last30">Last 30 days</SelectItem>
                <SelectItem value="last90">Last 90 days</SelectItem>
                <SelectItem value="year">This year</SelectItem>
              </SelectContent>
            </Select>
            <Button>
              <Download className="h-4 w-4 mr-2" />
              Export Report
            </Button>
          </div>
        </div>

        <Tabs defaultValue="services" className="space-y-6">
          <TabsList>
            <TabsTrigger value="services">Service Reports</TabsTrigger>
            <TabsTrigger value="cases">Case Reports</TabsTrigger>
            <TabsTrigger value="performance">Performance Metrics</TabsTrigger>
          </TabsList>

          <TabsContent value="services">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
              <Card className="p-4">
                <h3 className="text-sm font-medium text-gray-500">Total Applications</h3>
                <p className="text-2xl font-bold mt-1">{serviceStats.totalApplications}</p>
              </Card>
              <Card className="p-4">
                <h3 className="text-sm font-medium text-gray-500">Approved</h3>
                <p className="text-2xl font-bold mt-1 text-green-600">{serviceStats.approvedApplications}</p>
              </Card>
              <Card className="p-4">
                <h3 className="text-sm font-medium text-gray-500">Pending</h3>
                <p className="text-2xl font-bold mt-1 text-yellow-600">{serviceStats.pendingApplications}</p>
              </Card>
              <Card className="p-4">
                <h3 className="text-sm font-medium text-gray-500">Rejected</h3>
                <p className="text-2xl font-bold mt-1 text-red-600">{serviceStats.rejectedApplications}</p>
              </Card>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card className="p-6">
                <h3 className="text-lg font-semibold mb-4">Service Distribution</h3>
                <Suspense fallback={<ChartPlaceholder icon={PieChart} />}>
                  <Chart
                    options={serviceDistributionOptions}
                    series={serviceDistributionSeries}
                    type="pie"
                    height={300}
                  />
                </Suspense>
              </Card>
              <Card className="p-6">
                <h3 className="text-lg font-semibold mb-4">Monthly Applications</h3>
                <Suspense fallback={<ChartPlaceholder icon={LineChart} />}>
                  <Chart
                    options={monthlyApplicationsOptions}
                    series={monthlyApplicationsSeries}
                    type="line"
                    height={300}
                  />
                </Suspense>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="cases">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
              <Card className="p-4">
                <h3 className="text-sm font-medium text-gray-500">Total Cases</h3>
                <p className="text-2xl font-bold mt-1">{caseStats.totalCases}</p>
              </Card>
              <Card className="p-4">
                <h3 className="text-sm font-medium text-gray-500">Resolved</h3>
                <p className="text-2xl font-bold mt-1 text-green-600">{caseStats.resolvedCases}</p>
              </Card>
              <Card className="p-4">
                <h3 className="text-sm font-medium text-gray-500">Pending</h3>
                <p className="text-2xl font-bold mt-1 text-yellow-600">{caseStats.pendingCases}</p>
              </Card>
              <Card className="p-4">
                <h3 className="text-sm font-medium text-gray-500">Urgent</h3>
                <p className="text-2xl font-bold mt-1 text-red-600">{caseStats.urgentCases}</p>
              </Card>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card className="p-6">
                <h3 className="text-lg font-semibold mb-4">Case Types</h3>
                <Suspense fallback={<ChartPlaceholder icon={BarChart} />}>
                  <Chart
                    options={caseTypesOptions}
                    series={caseTypesSeries}
                    type="bar"
                    height={300}
                  />
                </Suspense>
              </Card>
              <Card className="p-6">
                <h3 className="text-lg font-semibold mb-4">Resolution Time</h3>
                <Suspense fallback={<ChartPlaceholder icon={BarChart} />}>
                  <Chart
                    options={resolutionTimeOptions}
                    series={resolutionTimeSeries}
                    type="bar"
                    height={300}
                  />
                </Suspense>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="performance">
            <Card className="p-6">
              <div className="flex justify-between items-center mb-6">
                <h3 className="text-lg font-semibold">Performance Metrics</h3>
                <Button variant="outline">
                  <Filter className="h-4 w-4 mr-2" />
                  Filter Metrics
                </Button>
              </div>
              <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <Card className="p-4">
                    <h4 className="text-sm font-medium text-gray-500">Cases Resolved Today</h4>
                    <p className="text-2xl font-bold mt-1">{dashboardData.resolvedToday}</p>
                    <p className="text-sm text-gray-500 mt-1">Out of {dashboardData.activeCases} active cases</p>
                  </Card>
                  <Card className="p-4">
                    <h4 className="text-sm font-medium text-gray-500">Processing Efficiency</h4>
                    <p className="text-2xl font-bold mt-1">
                      {Math.round((dashboardData.resolvedToday / (dashboardData.resolvedToday + dashboardData.pendingApplications)) * 100)}%
                    </p>
                    <p className="text-sm text-gray-500 mt-1">{dashboardData.pendingApplications} pending applications</p>
                  </Card>
                  <Card className="p-4">
                    <h4 className="text-sm font-medium text-gray-500">Verification Rate</h4>
                    <p className="text-2xl font-bold mt-1">
                      {Math.round(((dashboardData.totalCases - dashboardData.pendingVerifications) / dashboardData.totalCases) * 100)}%
                    </p>
                    <p className="text-sm text-gray-500 mt-1">{dashboardData.pendingVerifications} pending verifications</p>
                  </Card>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <Card className="p-6">
                    <h4 className="text-lg font-semibold mb-4">Weekly Performance Trends</h4>
                    <Suspense fallback={<ChartPlaceholder icon={LineChart} />}>
                      <Chart
                        options={performanceMetricsOptions}
                        series={performanceMetricsSeries}
                        type="area"
                        height={300}
                      />
                    </Suspense>
                  </Card>
                  <Card className="p-6">
                    <h4 className="text-lg font-semibold mb-4">Application Processing Time</h4>
                    <Suspense fallback={<ChartPlaceholder icon={BarChart} />}>
                      <Chart
                        options={processingTimeOptions}
                        series={processingTimeSeries}
                        type="bar"
                        height={300}
                      />
                    </Suspense>
                  </Card>
                </div>

                <Card className="p-6">
                  <h4 className="text-lg font-semibold mb-4">Service Efficiency Matrix</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <div className="p-4 bg-gray-50 rounded-lg">
                      <h5 className="text-sm font-medium text-gray-500">Average Processing Time</h5>
                      <p className="text-xl font-bold mt-1">2.5 days</p>
                      <p className="text-xs text-gray-500 mt-1">-0.5 days from last week</p>
                    </div>
                    <div className="p-4 bg-gray-50 rounded-lg">
                      <h5 className="text-sm font-medium text-gray-500">Service Utilization</h5>
                      <p className="text-xl font-bold mt-1">78%</p>
                      <p className="text-xs text-gray-500 mt-1">+5% from last month</p>
                    </div>
                    <div className="p-4 bg-gray-50 rounded-lg">
                      <h5 className="text-sm font-medium text-gray-500">Client Satisfaction</h5>
                      <p className="text-xl font-bold mt-1">4.5/5.0</p>
                      <p className="text-xs text-gray-500 mt-1">Based on 150 reviews</p>
                    </div>
                    <div className="p-4 bg-gray-50 rounded-lg">
                      <h5 className="text-sm font-medium text-gray-500">Resource Efficiency</h5>
                      <p className="text-xl font-bold mt-1">92%</p>
                      <p className="text-xs text-gray-500 mt-1">+2% from target</p>
                    </div>
                  </div>
                </Card>
              </div>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </AppLayout>
  );
} 