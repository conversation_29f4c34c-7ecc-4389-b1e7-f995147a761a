import { useCallback, useEffect, useState } from 'react';

export type Appearance = 'light';

const prefersDark = () => {
    return false;
};

const setCookie = (name: string, value: string, days = 365) => {
    if (typeof document === 'undefined') {
        return;
    }

    const maxAge = days * 24 * 60 * 60;
    document.cookie = `${name}=${value};path=/;max-age=${maxAge};SameSite=Lax`;
};

const applyTheme = () => {
    // Always use light theme
    document.documentElement.classList.remove('dark');
};

const mediaQuery = () => {
    return null;
};

const handleSystemThemeChange = () => {
    // No-op since we're always using light theme
};

export function initializeTheme() {
    // Always initialize with light theme
    applyTheme();
}

export function useAppearance() {
    const [appearance, setAppearance] = useState<Appearance>('light');

    const updateAppearance = useCallback(() => {
        setAppearance('light');

        // Store in localStorage for client-side persistence...
        localStorage.setItem('appearance', 'light');

        // Store in cookie for SSR...
        setCookie('appearance', 'light');

        applyTheme();
    }, []);

    useEffect(() => {
        updateAppearance();
        // No need to clean up event listeners since we don't add any
    }, [updateAppearance]);

    return { appearance, updateAppearance } as const;
}
