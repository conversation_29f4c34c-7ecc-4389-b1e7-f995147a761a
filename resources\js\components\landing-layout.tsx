import React from 'react';
import { Head, usePage, Link } from '@inertiajs/react';
import { type SharedData } from '@/types';
import LandingNav from '@/components/landing-nav';

interface LandingLayoutProps {
    children: React.ReactNode;
    title?: string;
}

export default function LandingLayout({ children, title }: LandingLayoutProps) {
    const { auth } = usePage<SharedData>().props;
    const currentPath = window.location.pathname;

    // Map the current path to route names
    let currentRoute = route('home');
    if (currentPath.includes('/services-info')) {
        currentRoute = route('services-info');
    } else if (currentPath.includes('/faqs')) {
        currentRoute = route('faqs');
    } else if (currentPath.includes('/contact')) {
        currentRoute = route('contact');
    }

    return (
        <>
            <Head title={title ? `${title} - Balagtas Social Care` : 'Balagtas Social Care'}>
                <link rel="preconnect" href="https://fonts.bunny.net" />
                <link href="https://fonts.bunny.net/css?family=instrument-sans:400,500,600" rel="stylesheet" />
            </Head>
            <div className="min-h-screen bg-gradient-to-b from-white to-purple-50 flex flex-col">
                <LandingNav currentPath={currentRoute} auth={auth} />
                
                {/* Page Header */}
                {title && (
                    <div className="bg-purple-700 text-white py-12">
                        <div className="container mx-auto px-4">
                            <h1 className="text-3xl font-bold">{title}</h1>
                        </div>
                    </div>
                )}

                {/* Main Content */}
                <main className="flex-grow">
                    {children}
                </main>

                {/* Footer */}
                <footer className="bg-gray-900 text-gray-300">
                    <div className="container mx-auto px-4 py-12">
                        <div className="grid md:grid-cols-4 gap-8">
                            <div>
                                <h3 className="text-white text-lg font-semibold mb-4">Balagtas Social Care</h3>
                                <p className="text-sm mb-3">
                                    The official social services platform for the residents of Balagtas.
                                </p>
                                <p className="text-sm text-purple-300">
                                    Managed and operated by the Municipal Social Welfare and Development Office (MSWDO) of Balagtas, Bulacan
                                </p>
                            </div>
                            <div>
                                <h3 className="text-white text-lg font-semibold mb-4">Quick Links</h3>
                                <ul className="space-y-2 text-sm">
                                    <li><Link href={route('home')} className="hover:text-white">Home</Link></li>
                                    <li><Link href={route('services-info')} className="hover:text-white">Services</Link></li>
                                    <li><Link href={route('faqs')} className="hover:text-white">FAQs</Link></li>
                                    <li><Link href={route('contact')} className="hover:text-white">Contact</Link></li>
                                </ul>
                            </div>
                            <div>
                                <h3 className="text-white text-lg font-semibold mb-4">Contact</h3>
                                <ul className="space-y-2 text-sm">
                                    <li>MSWDO - Municipal Hall</li>
                                    <li>Balagtas, Bulacan</li>
                                    <li>Phone: (*************</li>
                                    <li>Email: <EMAIL></li>
                                </ul>
                            </div>
                            <div>
                                <h3 className="text-white text-lg font-semibold mb-4">Office Hours</h3>
                                <ul className="space-y-2 text-sm">
                                    <li>Monday - Friday: 8:00 AM - 5:00 PM</li>
                                    <li>Saturday: 8:00 AM - 12:00 PM</li>
                                    <li>Sunday: Closed</li>
                                </ul>
                            </div>
                        </div>
                        <div className="border-t border-gray-800 mt-12 pt-8 text-sm text-center">
                            <p className="mb-2">© {new Date().getFullYear()} Balagtas Social Care. All rights reserved.</p>
                            <p className="text-gray-400">A digital initiative by MSWDO Balagtas to streamline social welfare services for our community.</p>
                        </div>
                    </div>
                </footer>
            </div>
        </>
    );
} 