<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ServiceRequirement extends Model
{
    use SoftDeletes;

    protected $fillable = [
        'service_id',
        'name',
        'description',
        'document_type',
        'max_file_size',
        'is_required',
        'validity_days',
        'validation_rules',
        'order',
        'is_active',
    ];

    protected $casts = [
        'max_file_size' => 'integer',
        'is_required' => 'boolean',
        'validity_days' => 'integer',
        'validation_rules' => 'array',
        'order' => 'integer',
        'is_active' => 'boolean',
    ];

    /**
     * Get the service that owns the requirement.
     */
    public function service(): BelongsTo
    {
        return $this->belongsTo(Service::class);
    }
}
