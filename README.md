# Balagtas Social Care

A comprehensive social services management system for the Municipality of Balagtas, designed to streamline the delivery of social welfare services to residents.

## Overview

Balagtas Social Care is a modern web application built to manage and facilitate social welfare services in the Municipality of Balagtas. The system provides role-specific interfaces for clients, administrators, and super administrators, enabling efficient service delivery and case management.

## Features

### Client Features
- Multi-step BFACES application form
- Document upload and verification
- Service application tracking
- Appointment scheduling
- Status monitoring
- Profile management

### Admin Features
- Case management system
- Document verification queue
- Interview scheduling
- Service application processing
- Client management
- Status tracking and updates

### Super Admin Features
- System-wide analytics and reporting
- Service management
- User and role management
- Database management
- System configuration
- Performance monitoring

## Tech Stack

- **Frontend**:
  - React with TypeScript
  - Inertia.js for SPA-like experience
  - Tailwind CSS with shadcn/ui components
  - ApexCharts for data visualization

- **Backend**:
  - Laravel 10
  - MySQL database
  - Laravel Breeze for authentication

## Development Setup

1. Clone the repository:
   ```bash
   git clone https://github.com/yourusername/balagtas-social-care.git
   cd balagtas-social-care
   ```

2. Install PHP dependencies:
   ```bash
   composer install
   ```

3. Install Node.js dependencies:
   ```bash
   npm install
   ```

4. Configure environment:
   ```bash
   cp .env.example .env
   php artisan key:generate
   ```

5. Set up the database:
   ```bash
   php artisan migrate
   ```

6. Start development servers:
   ```bash
   # Terminal 1 - Laravel server
   php artisan serve

   # Terminal 2 - Vite development server
   npm run dev
   ```

## Project Structure

```
balagtas-social-care/
├── app/                    # Laravel application code
├── resources/
│   ├── js/                # React components and pages
│   │   ├── components/    # Reusable UI components
│   │   ├── layouts/       # Page layouts
│   │   └── pages/        # Page components
│   └── views/            # Laravel views
├── routes/               # Laravel routes
├── public/              # Public assets
└── docs/               # Project documentation
    └── memory-bank/    # Development documentation
```

## Key Features

### Role-Based Access
- Client access for residents
- Admin access for social workers
- Super admin access for system management

### Document Management
- Secure document upload
- Verification workflow
- Status tracking
- Digital storage

### Service Management
- BFACES application processing
- Service application tracking
- Interview scheduling
- Case management

### Analytics & Reporting
- Service utilization metrics
- Processing time analytics
- Workload distribution
- Performance monitoring

## Development Approach

The project follows a frontend-first development approach:
1. Building comprehensive UI components
2. Using temporary data structures
3. Establishing user flows
4. Implementing backend features iteratively

## Contributing

1. Create a feature branch:
   ```bash
   git checkout -b feature/your-feature-name
   ```

2. Make your changes and commit:
   ```bash
   git commit -m "Description of changes"
   ```

3. Push to your branch:
   ```bash
   git push origin feature/your-feature-name
   ```

4. Create a Pull Request

## License

This project is proprietary software developed for the Municipality of Balagtas. All rights reserved.

## Contact

For inquiries about this project, please contact:
- Municipal Social Welfare and Development Office (MSWDO)
- Municipality of Balagtas, Bulacan
