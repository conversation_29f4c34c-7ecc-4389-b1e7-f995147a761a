import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, Card<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import AppLayout from "@/layouts/app-layout";
import { type BreadcrumbItem } from "@/types";
import { Head } from "@inertiajs/react";
import { AlertCircle, CheckCircle, Clock, Eye, FileCheck, FileText, HelpCircle, Timer } from "lucide-react";

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: "Dashboard",
        href: "/dashboard",
    },
    {
        title: "Applications",
        href: "/applications",
    },
];

// Dummy data for applications
const applications = [
    {
        id: 1,
        serviceType: "Medical Assistance",
        referenceNumber: "MED-2023-1234",
        dateSubmitted: "2023-04-05",
        status: "approved",
        lastUpdated: "2023-04-07",
        remarks: "Approved for claiming. Please bring original documents and valid ID.",
        documents: ["Medical Certificate", "Hospital Bill", "Barangay Certificate"],
        amount: 5000,
    },
    {
        id: 2,
        serviceType: "Burial Assistance",
        referenceNumber: "BUR-2023-0987",
        dateSubmitted: "2023-03-15",
        status: "pending",
        lastUpdated: "2023-03-20",
        remarks: "Documents under review. Interview scheduled on April 10, 2023.",
        documents: ["Death Certificate", "Funeral Parlor Receipt", "Barangay Certificate"],
        amount: 10000,
    },
    {
        id: 3,
        serviceType: "Educational Assistance",
        referenceNumber: "EDU-2023-5678",
        dateSubmitted: "2023-02-10",
        status: "rejected",
        lastUpdated: "2023-02-15",
        remarks: "Incomplete documentation. Please re-apply with complete school requirements.",
        documents: ["School ID", "Certificate of Enrollment"],
        amount: 3000,
    },
    {
        id: 4,
        serviceType: "Medical Assistance",
        referenceNumber: "MED-2022-4321",
        dateSubmitted: "2022-05-10",
        status: "completed",
        lastUpdated: "2022-05-15",
        remarks: "Assistance provided. Service cooldown until May 10, 2023.",
        documents: ["Medical Certificate", "Hospital Bill", "Barangay Certificate"],
        amount: 5000,
    },
];

// Helper function for status badge
const getStatusBadge = (status: string) => {
    switch (status) {
        case "pending":
            return (
                <div className="flex items-center gap-1.5 rounded-full bg-purple-100 px-2 py-1 text-xs font-medium text-purple-800">
                    <Clock className="h-3 w-3" />
                    <span>Pending</span>
                </div>
            );
        case "approved":
            return (
                <div className="flex items-center gap-1.5 rounded-full bg-green-100 px-2 py-1 text-xs font-medium text-green-800">
                    <CheckCircle className="h-3 w-3" />
                    <span>Approved</span>
                </div>
            );
        case "rejected":
            return (
                <div className="flex items-center gap-1.5 rounded-full bg-red-100 px-2 py-1 text-xs font-medium text-red-800">
                    <AlertCircle className="h-3 w-3" />
                    <span>Rejected</span>
                </div>
            );
        case "completed":
            return (
                <div className="flex items-center gap-1.5 rounded-full bg-blue-100 px-2 py-1 text-xs font-medium text-blue-800">
                    <FileCheck className="h-3 w-3" />
                    <span>Completed</span>
                </div>
            );
        default:
            return (
                <div className="flex items-center gap-1.5 rounded-full bg-gray-100 px-2 py-1 text-xs font-medium text-gray-800">
                    <HelpCircle className="h-3 w-3" />
                    <span>Unknown</span>
                </div>
            );
    }
};

export default function Applications() {
    const activeApplications = applications.filter((app) => ["pending", "approved"].includes(app.status));
    const completedApplications = applications.filter((app) => app.status === "completed");
    const rejectedApplications = applications.filter((app) => app.status === "rejected");

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Applications" />
            
            <div className="flex flex-col gap-6 p-6">
                <div className="flex items-center justify-between">
                    <h1 className="text-2xl font-bold tracking-tight">My Applications</h1>
                    <Button className="bg-purple-600 hover:bg-purple-700" asChild>
                        <a href="/services">Apply for Service</a>
                    </Button>
                </div>

                <div className="rounded-lg border p-4 bg-purple-50/50">
                    <div className="flex items-start gap-3">
                        <Timer className="mt-0.5 h-5 w-5 text-purple-400 shrink-0" />
                        <div>
                            <h3 className="text-sm font-medium">Service Application Process</h3>
                            <p className="text-sm text-muted-foreground">
                                After submitting your application, it will be reviewed by our staff. This typically 
                                takes 1-3 business days. You may be contacted for an interview or requested to provide 
                                additional documents. Approved services can be claimed at the Balagtas Municipal Hall.
                            </p>
                        </div>
                    </div>
                </div>

                <Tabs defaultValue="active" className="w-full">
                    <TabsList className="grid w-full grid-cols-3 bg-purple-50">
                        <TabsTrigger value="active" className="data-[state=active]:bg-purple-600 data-[state=active]:text-white">
                            Active ({activeApplications.length})
                        </TabsTrigger>
                        <TabsTrigger value="completed" className="data-[state=active]:bg-purple-600 data-[state=active]:text-white">
                            Completed ({completedApplications.length})
                        </TabsTrigger>
                        <TabsTrigger value="rejected" className="data-[state=active]:bg-purple-600 data-[state=active]:text-white">
                            Rejected ({rejectedApplications.length})
                        </TabsTrigger>
                    </TabsList>
                    
                    <TabsContent value="active" className="mt-6">
                        {activeApplications.length > 0 ? (
                            <div className="space-y-4">
                                {activeApplications.map((application) => (
                                    <Card key={application.id}>
                                        <CardHeader className="pb-2">
                                            <div className="flex flex-col justify-between gap-2 sm:flex-row sm:items-center">
                                                <div>
                                                    <CardTitle>{application.serviceType}</CardTitle>
                                                    <CardDescription>
                                                        Reference: {application.referenceNumber}
                                                    </CardDescription>
                                                </div>
                                                {getStatusBadge(application.status)}
                                            </div>
                                        </CardHeader>
                                        <CardContent className="pb-3">
                                            <div className="space-y-2">
                                                <div className="grid grid-cols-2 gap-4">
                                                    <div>
                                                        <p className="text-sm font-medium">Date Submitted</p>
                                                        <p className="text-sm text-muted-foreground">
                                                            {application.dateSubmitted}
                                                        </p>
                                                    </div>
                                                    <div>
                                                        <p className="text-sm font-medium">Last Updated</p>
                                                        <p className="text-sm text-muted-foreground">
                                                            {application.lastUpdated}
                                                        </p>
                                                    </div>
                                                </div>
                                                <div>
                                                    <p className="text-sm font-medium">Status Remarks</p>
                                                    <p className="text-sm text-muted-foreground">
                                                        {application.remarks}
                                                    </p>
                                                </div>
                                                <div>
                                                    <p className="text-sm font-medium">Amount</p>
                                                    <p className="text-sm text-muted-foreground">
                                                        ₱{application.amount.toLocaleString()}
                                                    </p>
                                                </div>
                                            </div>
                                        </CardContent>
                                        <CardFooter className="flex justify-between bg-muted/50 pt-3">
                                            <div className="flex items-center">
                                                <FileText className="mr-1.5 h-4 w-4 text-muted-foreground" />
                                                <span className="text-xs text-muted-foreground">
                                                    {application.documents.length} Documents
                                                </span>
                                            </div>
                                            <Button size="sm" variant="outline" asChild>
                                                <a href={`/applications/${application.id}`}>
                                                    <Eye className="mr-1.5 h-3 w-3" /> View Details
                                                </a>
                                            </Button>
                                        </CardFooter>
                                    </Card>
                                ))}
                            </div>
                        ) : (
                            <div className="flex flex-col items-center justify-center py-12 text-center">
                                <HelpCircle className="h-12 w-12 text-purple-400 mb-3" />
                                <h3 className="text-lg font-medium">No Active Applications</h3>
                                <p className="text-sm text-muted-foreground max-w-md mt-2">
                                    You don't have any active applications at the moment. Apply for a service to get started.
                                </p>
                                <Button className="mt-4 bg-purple-600 hover:bg-purple-700" asChild>
                                    <a href="/services">Browse Services</a>
                                </Button>
                            </div>
                        )}
                    </TabsContent>
                    
                    <TabsContent value="completed" className="mt-6">
                        {completedApplications.length > 0 ? (
                            <div className="space-y-4">
                                {completedApplications.map((application) => (
                                    <Card key={application.id}>
                                        <CardHeader className="pb-2">
                                            <div className="flex flex-col justify-between gap-2 sm:flex-row sm:items-center">
                                                <div>
                                                    <CardTitle>{application.serviceType}</CardTitle>
                                                    <CardDescription>
                                                        Reference: {application.referenceNumber}
                                                    </CardDescription>
                                                </div>
                                                {getStatusBadge(application.status)}
                                            </div>
                                        </CardHeader>
                                        <CardContent className="pb-3">
                                            <div className="space-y-2">
                                                <div className="grid grid-cols-2 gap-4">
                                                    <div>
                                                        <p className="text-sm font-medium">Date Submitted</p>
                                                        <p className="text-sm text-muted-foreground">
                                                            {application.dateSubmitted}
                                                        </p>
                                                    </div>
                                                    <div>
                                                        <p className="text-sm font-medium">Completed On</p>
                                                        <p className="text-sm text-muted-foreground">
                                                            {application.lastUpdated}
                                                        </p>
                                                    </div>
                                                </div>
                                                <div>
                                                    <p className="text-sm font-medium">Status Remarks</p>
                                                    <p className="text-sm text-muted-foreground">
                                                        {application.remarks}
                                                    </p>
                                                </div>
                                                <div>
                                                    <p className="text-sm font-medium">Amount Received</p>
                                                    <p className="text-sm text-muted-foreground">
                                                        ₱{application.amount.toLocaleString()}
                                                    </p>
                                                </div>
                                            </div>
                                        </CardContent>
                                        <CardFooter className="flex justify-between bg-muted/50 pt-3">
                                            <div className="flex items-center">
                                                <FileText className="mr-1.5 h-4 w-4 text-muted-foreground" />
                                                <span className="text-xs text-muted-foreground">
                                                    {application.documents.length} Documents
                                                </span>
                                            </div>
                                            <Button size="sm" variant="outline" asChild>
                                                <a href={`/applications/${application.id}`}>
                                                    <Eye className="mr-1.5 h-3 w-3" /> View Details
                                                </a>
                                            </Button>
                                        </CardFooter>
                                    </Card>
                                ))}
                            </div>
                        ) : (
                            <div className="flex flex-col items-center justify-center py-12 text-center">
                                <FileCheck className="h-12 w-12 text-purple-400 mb-3" />
                                <h3 className="text-lg font-medium">No Completed Applications</h3>
                                <p className="text-sm text-muted-foreground max-w-md mt-2">
                                    You don't have any completed applications yet.
                                </p>
                            </div>
                        )}
                    </TabsContent>
                    
                    <TabsContent value="rejected" className="mt-6">
                        {rejectedApplications.length > 0 ? (
                            <div className="space-y-4">
                                {rejectedApplications.map((application) => (
                                    <Card key={application.id}>
                                        <CardHeader className="pb-2">
                                            <div className="flex flex-col justify-between gap-2 sm:flex-row sm:items-center">
                                                <div>
                                                    <CardTitle>{application.serviceType}</CardTitle>
                                                    <CardDescription>
                                                        Reference: {application.referenceNumber}
                                                    </CardDescription>
                                                </div>
                                                {getStatusBadge(application.status)}
                                            </div>
                                        </CardHeader>
                                        <CardContent className="pb-3">
                                            <div className="space-y-2">
                                                <div className="grid grid-cols-2 gap-4">
                                                    <div>
                                                        <p className="text-sm font-medium">Date Submitted</p>
                                                        <p className="text-sm text-muted-foreground">
                                                            {application.dateSubmitted}
                                                        </p>
                                                    </div>
                                                    <div>
                                                        <p className="text-sm font-medium">Rejected On</p>
                                                        <p className="text-sm text-muted-foreground">
                                                            {application.lastUpdated}
                                                        </p>
                                                    </div>
                                                </div>
                                                <div>
                                                    <p className="text-sm font-medium">Rejection Reason</p>
                                                    <p className="text-sm text-muted-foreground">
                                                        {application.remarks}
                                                    </p>
                                                </div>
                                            </div>
                                        </CardContent>
                                        <CardFooter className="flex justify-between bg-muted/50 pt-3">
                                            <div className="flex items-center">
                                                <FileText className="mr-1.5 h-4 w-4 text-muted-foreground" />
                                                <span className="text-xs text-muted-foreground">
                                                    {application.documents.length} Documents
                                                </span>
                                            </div>
                                            <div className="flex gap-2">
                                                <Button size="sm" variant="outline" asChild>
                                                    <a href={`/applications/${application.id}`}>
                                                        <Eye className="mr-1.5 h-3 w-3" /> View Details
                                                    </a>
                                                </Button>
                                                <Button size="sm" asChild>
                                                    <a href={`/services`}>
                                                        Apply Again
                                                    </a>
                                                </Button>
                                            </div>
                                        </CardFooter>
                                    </Card>
                                ))}
                            </div>
                        ) : (
                            <div className="flex flex-col items-center justify-center py-12 text-center">
                                <CheckCircle className="h-12 w-12 text-purple-400 mb-3" />
                                <h3 className="text-lg font-medium">No Rejected Applications</h3>
                                <p className="text-sm text-muted-foreground max-w-md mt-2">
                                    You don't have any rejected applications.
                                </p>
                            </div>
                        )}
                    </TabsContent>
                </Tabs>
            </div>
        </AppLayout>
    );
} 