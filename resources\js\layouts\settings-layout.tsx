import { type BreadcrumbItem } from "@/types";
import AppLayout from "@/layouts/app/app-sidebar-layout";
import { SettingsNav } from "@/components/settings-nav";

interface SettingsLayoutProps {
  children: React.ReactNode;
  breadcrumbs: BreadcrumbItem[];
  role: "client" | "admin" | "superadmin";
  current: "profile" | "password";
}

export default function SettingsLayout({ children, breadcrumbs, role, current }: SettingsLayoutProps) {
  return (
    <AppLayout breadcrumbs={breadcrumbs}>
      <div className="container py-6">
        <div className="mb-6">
          <h2 className="text-2xl font-bold tracking-tight">Settings</h2>
          <p className="text-muted-foreground">
            Manage your account settings and preferences.
          </p>
        </div>

        <div className="flex gap-6">
          <SettingsNav role={role} current={current} />
          <div className="flex-1">{children}</div>
        </div>
      </div>
    </AppLayout>
  );
} 