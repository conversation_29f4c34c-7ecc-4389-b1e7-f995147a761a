<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('services', function (Blueprint $table) {
            $table->id();
            $table->foreignId('category_id')->constrained('service_categories')->onDelete('restrict');
            $table->string('name');
            $table->text('description')->nullable();
            $table->string('slug')->unique();
            $table->decimal('max_amount', 10, 2)->nullable();
            $table->integer('processing_days')->default(1);
            $table->integer('daily_quota')->nullable();
            $table->integer('weekly_quota')->nullable();
            $table->integer('monthly_quota')->nullable();
            $table->json('eligibility_criteria')->nullable();
            $table->json('schedule')->nullable();
            $table->boolean('requires_verification')->default(true);
            $table->boolean('is_active')->default(true);
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('services');
    }
};
