import { Application, DashboardData, ScheduleItem } from './types';

export const recentApplications: Application[] = [
    {
        id: '1',
        clientId: '101',
        clientName: '<PERSON>',
        service: 'Medical Assistance',
        status: 'pending',
        priority: 'high',
        submittedAt: '2024-04-08',
        lastUpdated: '2024-04-08'
    },
    {
        id: '2',
        clientId: '102',
        clientName: '<PERSON>',
        service: 'Burial Assistance',
        status: 'approved',
        priority: 'medium',
        submittedAt: '2024-04-07',
        lastUpdated: '2024-04-07'
    },
    {
        id: '3',
        clientId: '103',
        clientName: '<PERSON>',
        service: 'Educational Aid',
        status: 'in_progress',
        priority: 'low',
        submittedAt: '2024-04-07',
        lastUpdated: '2024-04-07'
    }
];

export const todaySchedule: ScheduleItem[] = [
    {
        id: '1',
        clientId: '201',
        clientName: '<PERSON>',
        purpose: 'Initial Interview',
        date: '2024-04-09',
        time: '9:00 AM',
        status: 'scheduled'
    },
    {
        id: '2',
        clientId: '202',
        clientName: '<PERSON>',
        purpose: 'Document Verification',
        date: '2024-04-09',
        time: '11:30 AM',
        status: 'scheduled'
    },
    {
        id: '3',
        clientId: '203',
        clientName: 'Maria Garcia',
        purpose: 'Follow-up Meeting',
        date: '2024-04-09',
        time: '2:00 PM',
        status: 'scheduled'
    }
];

export const dashboardData: DashboardData = {
    totalCases: 156,
    activeCases: 42,
    pendingVerifications: 23,
    urgentCases: 8,
    todayAppointments: 5,
    pendingApplications: 18,
    resolvedToday: 12,
    recentActivity: [
        {
            type: 'case_assigned',
            description: 'New medical assistance case assigned',
            client: 'Maria Santos',
            time: '1 hour ago'
        },
        {
            type: 'verification_completed',
            description: 'Residency verification completed',
            client: 'Jose Garcia',
            time: '2 hours ago'
        },
        {
            type: 'application_approved',
            description: 'BFACES application approved',
            client: 'Ana Reyes',
            time: '3 hours ago'
        }
    ]
};

export const dashboardMetrics = {
    totalApplications: {
        value: 128,
        change: '+14 from last month'
    },
    pendingReview: {
        value: 23,
        change: '+7 since yesterday'
    },
    activeServices: {
        value: 12,
        change: '2 need attention'
    },
    todayAppointments: {
        value: 8,
        change: '3 completed'
    }
}; 