import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head } from '@inertiajs/react';
import { Users, Settings, Shield, AlertCircle, BarChart2, UserCog, FileText, Database, Clock, CheckCircle } from 'lucide-react';
import { stats as userStats } from '@/pages/superadmin/tempData/users';
import { stats as serviceStats } from '@/pages/superadmin/tempData/services';
import { recentActivity, quickStats, serviceMetrics, systemMetrics } from '@/pages/superadmin/tempData/dashboard';
import { Suspense } from 'react';
import { StatCardsSkeleton, CardWithActionsSkeleton, TableRowSkeleton } from '@/components/skeletons/shared-skeletons';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/superadmin/dashboard',
    },
];

export default function SuperAdminDashboard() {
    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Super Admin Dashboard" />
            
            <div className="flex flex-col gap-6 p-6">
                <div className="flex items-center justify-between">
                    <h1 className="text-2xl font-bold tracking-tight">System Administration Dashboard</h1>
                </div>

                {/* System Overview */}
                <Suspense fallback={<StatCardsSkeleton />}>
                    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between pb-2">
                                <CardTitle className="text-sm font-medium">Total Users</CardTitle>
                                <Users className="h-5 w-5 text-muted-foreground" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold">{userStats.totalUsers}</div>
                                <CardDescription>
                                    {userStats.verificationStatus.verified} verified users
                                </CardDescription>
                            </CardContent>
                        </Card>
                        
                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between pb-2">
                                <CardTitle className="text-sm font-medium">Active Social Workers</CardTitle>
                                <UserCog className="h-5 w-5 text-muted-foreground" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold">{quickStats.activeSocialWorkers}</div>
                                <CardDescription>
                                    Currently on duty
                                </CardDescription>
                            </CardContent>
                        </Card>
                        
                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between pb-2">
                                <CardTitle className="text-sm font-medium">Pending Applications</CardTitle>
                                <Clock className="h-5 w-5 text-muted-foreground" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold">{quickStats.pendingApplications}</div>
                                <CardDescription>
                                    Awaiting review
                                </CardDescription>
                            </CardContent>
                        </Card>
                        
                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between pb-2">
                                <CardTitle className="text-sm font-medium">System Health</CardTitle>
                                <AlertCircle className="h-5 w-5 text-green-500" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold text-green-600">{systemMetrics.uptime}% Uptime</div>
                                <CardDescription>
                                    {systemMetrics.responseTime}ms response time
                                </CardDescription>
                            </CardContent>
                        </Card>
                    </div>
                </Suspense>

                {/* Service Statistics */}
                <h2 className="mt-4 text-xl font-semibold">Service Statistics</h2>
                <Suspense fallback={<div className="grid gap-4 md:grid-cols-3"><StatCardsSkeleton /></div>}>
                    <div className="grid gap-4 md:grid-cols-3">
                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between pb-2">
                                <CardTitle className="text-sm font-medium">Most Requested Service</CardTitle>
                                <FileText className="h-5 w-5 text-muted-foreground" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold">{serviceMetrics.mostRequested[0].name}</div>
                                <CardDescription>
                                    {serviceMetrics.mostRequested[0].applications} applications
                                </CardDescription>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between pb-2">
                                <CardTitle className="text-sm font-medium">Today's Appointments</CardTitle>
                                <CheckCircle className="h-5 w-5 text-muted-foreground" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold">{quickStats.todayAppointments}</div>
                                <CardDescription>
                                    Scheduled appointments
                                </CardDescription>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between pb-2">
                                <CardTitle className="text-sm font-medium">Storage Used</CardTitle>
                                <Database className="h-5 w-5 text-muted-foreground" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold">{systemMetrics.storageUsed}GB</div>
                                <CardDescription>
                                    Last backup: {systemMetrics.lastBackup}
                                </CardDescription>
                            </CardContent>
                        </Card>
                    </div>
                </Suspense>

                {/* Recent Activity */}
                <h2 className="mt-4 text-xl font-semibold">System Activity</h2>
                <Suspense fallback={
                    <Card>
                        <CardContent className="p-0">
                            <div className="divide-y">
                                {[...Array(5)].map((_, i) => (
                                    <TableRowSkeleton key={i} />
                                ))}
                            </div>
                        </CardContent>
                    </Card>
                }>
                    <Card>
                        <CardContent className="p-0">
                            <div className="divide-y">
                                {recentActivity.map((activity, index) => (
                                    <div key={index} className="flex items-center justify-between p-4">
                                        <div className="space-y-1">
                                            <p className="text-sm font-medium leading-none">{activity.description}</p>
                                            <p className="text-sm text-muted-foreground">{activity.details}</p>
                                        </div>
                                        <div className="text-sm text-muted-foreground">{activity.time}</div>
                                    </div>
                                ))}
                            </div>
                        </CardContent>
                    </Card>
                </Suspense>

                {/* Quick Actions */}
                <h2 className="mt-4 text-xl font-semibold">Administrative Actions</h2>
                <Suspense fallback={<div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">{[...Array(4)].map((_, i) => <CardWithActionsSkeleton key={i} />)}</div>}>
                    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                        <a href="users">
                        <Card className="cursor-pointer hover:bg-accent transition-colors">
                            <CardContent className="flex flex-col items-center justify-center p-6">
                                <UserCog className="h-8 w-8 text-muted-foreground mb-2" />
                                <h3 className="font-medium">Manage Users</h3>
                            </CardContent>
                        </Card>
                        </a>
                        <a href="configuration">
                        <Card className="cursor-pointer hover:bg-accent transition-colors">
                            <CardContent className="flex flex-col items-center justify-center p-6">
                                <Settings className="h-8 w-8 text-muted-foreground mb-2" />
                                <h3 className="font-medium">System Settings</h3>
                            </CardContent>
                        </Card>
                        </a>
                        <a href="services">
                        <Card className="cursor-pointer hover:bg-accent transition-colors">
                            <CardContent className="flex flex-col items-center justify-center p-6">
                                <FileText className="h-8 w-8 text-muted-foreground mb-2" />
                                <h3 className="font-medium">Service Config</h3>
                            </CardContent>
                        </Card>
                        </a>
                        <a href="database">
                        <Card className="cursor-pointer hover:bg-accent transition-colors">
                            <CardContent className="flex flex-col items-center justify-center p-6">
                                <Database className="h-8 w-8 text-muted-foreground mb-2" />
                                <h3 className="font-medium">Backup Data</h3>
                            </CardContent>
                        </Card>
                        </a>
                    </div>
                </Suspense>
            </div>
        </AppLayout>
    );
} 