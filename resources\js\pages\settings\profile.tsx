import { type BreadcrumbItem, type SharedData } from '@/types';
import { Transition } from '@headlessui/react';
import { Head, Link, useForm, usePage } from '@inertiajs/react';
import { FormEventHandler } from 'react';
import { CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Button } from "@/components/ui/button"
import SettingsLayout from "@/layouts/settings/settings-layout"

import DeleteUser from '@/components/delete-user';
import HeadingSmall from '@/components/heading-small';
import InputError from '@/components/input-error';
import AppLayout from '@/layouts/app-layout';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Profile settings',
        href: '/settings/profile',
    },
];

interface Props {
  role: "client" | "admin" | "superadmin"
  user: {
    name: string
    email: string
    phone?: string
    barangay?: string
    address?: string
  }
}

export default function Profile({ role = "client", user }: Props) {
  console.log("Profile props:", { role, user });

  if (!user) {
    console.error("User prop is undefined");
    return null; // or return a loading state
  }

  const { data, setData, patch, errors, processing } = useForm({
    name: user?.name || "",
    email: user?.email || "",
    phone: user?.phone || "",
    barangay: user?.barangay || "",
    address: user?.address || "",
  });

  function submit(e: React.FormEvent) {
    e.preventDefault();
    console.log("Submitting form with data:", data);
    patch(`/${role}/settings/profile`);
  }

  return (
    <AppLayout breadcrumbs={breadcrumbs}>
      <Head title="Profile Settings" />

      <SettingsLayout role={role}>
        <form onSubmit={submit}>
          <CardHeader>
            <CardTitle>Profile</CardTitle>
            <CardDescription>
              Update your personal information and contact details.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name">Full Name</Label>
                <Input
                  id="name"
                  type="text"
                  value={data.name}
                  onChange={e => setData("name", e.target.value)}
                />
                {errors.name && (
                  <p className="text-sm text-red-500">{errors.name}</p>
                )}
              </div>
              <div className="space-y-2">
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  type="email"
                  value={data.email}
                  onChange={e => setData("email", e.target.value)}
                />
                {errors.email && (
                  <p className="text-sm text-red-500">{errors.email}</p>
                )}
              </div>
              <div className="space-y-2">
                <Label htmlFor="phone">Phone Number</Label>
                <Input
                  id="phone"
                  type="tel"
                  value={data.phone}
                  onChange={e => setData("phone", e.target.value)}
                />
                {errors.phone && (
                  <p className="text-sm text-red-500">{errors.phone}</p>
                )}
              </div>
              {role === "client" && (
                <>
                  <div className="space-y-2">
                    <Label htmlFor="barangay">Barangay</Label>
                    <Input
                      id="barangay"
                      type="text"
                      value={data.barangay}
                      onChange={e => setData("barangay", e.target.value)}
                    />
                    {errors.barangay && (
                      <p className="text-sm text-red-500">{errors.barangay}</p>
                    )}
                  </div>
                  <div className="col-span-2 space-y-2">
                    <Label htmlFor="address">Complete Address</Label>
                    <Input
                      id="address"
                      type="text"
                      value={data.address}
                      onChange={e => setData("address", e.target.value)}
                    />
                    {errors.address && (
                      <p className="text-sm text-red-500">{errors.address}</p>
                    )}
                  </div>
                </>
              )}
            </div>
            <div className="flex justify-end">
              <Button type="submit" disabled={processing}>
                Save Changes
              </Button>
            </div>
          </CardContent>
        </form>

        <DeleteUser />
      </SettingsLayout>
    </AppLayout>
  )
}
