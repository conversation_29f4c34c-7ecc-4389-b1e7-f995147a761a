import { Head, useForm } from '@inertiajs/react';
import { Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Button } from "@/components/ui/button"
import DeleteUser from '@/components/delete-user';
import AppLayout from '@/layouts/app-layout';
import SettingsLayout from "@/layouts/settings/settings-layout"
import { type BreadcrumbItem } from '@/types';

interface Props {
  user: {
    name: string
    email: string
    phone?: string
  }
}

export default function SuperadminProfile({ user }: Props) {
  if (!user) {
    return null; // or return a loading state
  }

  const { data, setData, patch, errors, processing } = useForm({
    name: user?.name || "",
    email: user?.email || "",
    phone: user?.phone || "",
  });

  function submit(e: React.FormEvent) {
    e.preventDefault();
    patch('/superadmin/settings/profile');
  }

  return (
    <SettingsLayout role="superadmin">
      <Head title="Superadmin Profile Settings" />

      <form onSubmit={submit}>
        <CardHeader>
          <CardTitle>Profile</CardTitle>
          <CardDescription>
            Update your personal information and contact details.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="name">Full Name</Label>
              <Input
                id="name"
                type="text"
                value={data.name}
                onChange={e => setData("name", e.target.value)}
              />
              {errors.name && (
                <p className="text-sm text-red-500">{errors.name}</p>
              )}
            </div>
            <div className="space-y-2">
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                type="email"
                value={data.email}
                onChange={e => setData("email", e.target.value)}
              />
              {errors.email && (
                <p className="text-sm text-red-500">{errors.email}</p>
              )}
            </div>
            <div className="space-y-2">
              <Label htmlFor="phone">Phone Number</Label>
              <Input
                id="phone"
                type="tel"
                value={data.phone}
                onChange={e => setData("phone", e.target.value)}
              />
              {errors.phone && (
                <p className="text-sm text-red-500">{errors.phone}</p>
              )}
            </div>
          </div>
          <div className="flex justify-end">
            <Button type="submit" disabled={processing}>
              Save Changes
            </Button>
          </div>
        </CardContent>
      </form>

      <DeleteUser />
    </SettingsLayout>
  )
} 