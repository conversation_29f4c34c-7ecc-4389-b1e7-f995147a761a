export const recentActivity = [
    {
        type: 'user_registered',
        description: 'New social worker account created',
        details: 'Account pending approval',
        time: '30 minutes ago',
        icon: 'user'
    },
    {
        type: 'service_updated',
        description: 'Medical assistance service updated',
        details: 'Requirements modified',
        time: '1 hour ago',
        icon: 'file'
    },
    {
        type: 'system_backup',
        description: 'System backup completed',
        details: 'All databases backed up successfully',
        time: '2 hours ago',
        icon: 'database'
    },
    {
        type: 'application_approved',
        description: 'Medical assistance application approved',
        details: 'Approved by SW-001',
        time: '3 hours ago',
        icon: 'check'
    },
    {
        type: 'budget_allocated',
        description: 'Monthly budget allocation updated',
        details: 'Educational assistance fund increased',
        time: '4 hours ago',
        icon: 'wallet'
    }
];

export const quickStats = {
    pendingApplications: 124,
    pendingVerifications: 45,
    activeSocialWorkers: 12,
    todayAppointments: 28
};

export const serviceMetrics = {
    mostRequested: [
        {
            name: 'Medical Assistance',
            applications: 856,
            successRate: 92
        },
        {
            name: 'Educational Support',
            applications: 634,
            successRate: 88
        },
        {
            name: 'Housing Assistance',
            applications: 445,
            successRate: 76
        }
    ],
    applicationTrends: [
        { month: 'Jan', count: 180 },
        { month: 'Feb', count: 210 },
        { month: 'Mar', count: 245 },
        { month: 'Apr', count: 198 },
        { month: 'May', count: 267 },
        { month: 'Jun', count: 289 }
    ]
};

export const systemMetrics = {
    uptime: 99.9,
    responseTime: 245, // ms
    errorRate: 0.02,
    storageUsed: 85, // GB
    lastBackup: '2024-04-09 02:00 AM',
    nextMaintenance: '2024-04-14 02:00 AM'
}; 