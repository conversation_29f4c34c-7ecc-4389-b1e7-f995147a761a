import { cn } from "@/lib/utils";
import { <PERSON> } from "@inertiajs/react";
import { User2, Lock } from "lucide-react";

interface SettingsNavProps {
  role: "client" | "admin" | "superadmin";
  current: "profile" | "password";
}

export function SettingsNav({ role, current }: SettingsNavProps) {
  const baseUrl = `/${role}/settings`;
  const items = [
    {
      title: "Profile",
      href: `${baseUrl}/profile`,
      icon: User2
    },
    {
      title: "Password",
      href: `${baseUrl}/password`,
      icon: Lock
    },
  ];

  return (
    <nav className="w-64 space-y-1">
      {items.map((item) => {
        const Icon = item.icon;
        return (
          <Link
            key={item.href}
            href={item.href}
            className={cn(
              "flex items-center gap-3 rounded-lg px-3 py-2 text-sm font-medium transition-all hover:bg-accent",
              current === item.title.toLowerCase()
                ? "bg-accent text-accent-foreground"
                : "text-muted-foreground"
            )}
          >
            <Icon className="h-4 w-4" />
            {item.title}
          </Link>
        );
      })}
    </nav>
  );
} 