import React from 'react';

export default function AppLogo() {
    return (
        <div className="flex items-center gap-2">
            <img 
                src="/images/main-logo.png" 
                alt="Balagtas Social Care Logo" 
                className="h-10 w-auto"
                onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    // Fallback to the original icon if image fails to load
                    console.error('Logo image failed to load');
                }}
            />
            
            {/* this div i want to hide it when it gets to sm screen sizes */}
            <div className="grid flex-1 text-left text-sm hidden md:grid ">
                <span className="mb-0.5 truncate leading-none font-semibold text-purple-900">Balagtas Social Care</span>
                <span className="text-xs text-purple-600">Municipality of Balagtas</span>
            </div>
        </div>
    );
}
