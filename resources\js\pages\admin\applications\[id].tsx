import { Head } from "@inertiajs/react";
import AppLayout from "@/layouts/app/app-sidebar-layout";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Separator } from "@/components/ui/separator";
import { type BreadcrumbItem } from "@/types";
import { 
  User, 
  Mail, 
  Phone, 
  FileText, 
  Clock, 
  Calendar, 
  CheckCircle, 
  XCircle, 
  ArrowLeft, 
  AlertCircle,
  MessageSquare,
  Home,
  Download,
  Edit
} from "lucide-react";
import { useState } from "react";

interface ApplicationDetail {
  id: string;
  service: string;
  status: "pending" | "in_review" | "approved" | "rejected" | "completed";
  priority: "normal" | "urgent" | "critical";
  submittedDate: string;
  lastUpdated: string;
  assignedTo?: string;
  notes: string[];
  description: string;
  requestedAmount?: number;
  approvedAmount?: number;
  timeline: {
    date: string;
    status: string;
    description: string;
    actor: string;
  }[];
}

interface Client {
  id: string;
  name: string;
  email: string;
  phone: string;
  barangay: string;
  address: string;
  status: "verified" | "pending" | "inactive";
}

interface Document {
  id: string;
  name: string;
  type: string;
  uploadDate: string;
  status: "verified" | "pending" | "rejected";
  fileUrl: string;
}

// Dummy data for demonstration
const dummyApplication: ApplicationDetail = {
  id: "APP001",
  service: "Medical Assistance",
  status: "in_review",
  priority: "urgent",
  submittedDate: "2024-04-08",
  lastUpdated: "2024-04-09",
  assignedTo: "Maria Garcia",
  notes: [
    "Client has requested assistance for emergency surgery.",
    "Medical certificate verified and validated.",
    "Follow up with hospital for cost estimates."
  ],
  description: "Requesting financial assistance for gallbladder surgery scheduled for April 15, 2024 at Balagtas General Hospital. Total estimated cost is PHP 45,000.",
  requestedAmount: 15000,
  timeline: [
    {
      date: "2024-04-08 10:15 AM",
      status: "Submitted",
      description: "Application submitted through online portal",
      actor: "Juan Dela Cruz"
    },
    {
      date: "2024-04-08 02:30 PM",
      status: "Received",
      description: "Application received and marked for priority processing",
      actor: "System"
    },
    {
      date: "2024-04-09 09:45 AM",
      status: "In Review",
      description: "Application assigned to Maria Garcia for processing",
      actor: "Pedro Manalo"
    }
  ]
};

const dummyClient: Client = {
  id: "1",
  name: "Juan Dela Cruz",
  email: "<EMAIL>",
  phone: "09123456789",
  barangay: "San Miguel",
  address: "123 Main St., San Miguel, Balagtas",
  status: "verified"
};

const dummyDocuments: Document[] = [
  {
    id: "DOC001",
    name: "Medical Certificate",
    type: "Medical Document",
    uploadDate: "2024-04-08",
    status: "verified",
    fileUrl: "/documents/medical-certificate.pdf"
  },
  {
    id: "DOC002",
    name: "Hospital Estimate",
    type: "Financial Document",
    uploadDate: "2024-04-08",
    status: "verified",
    fileUrl: "/documents/hospital-bill.pdf"
  },
  {
    id: "DOC003",
    name: "Barangay Indigency Certificate",
    type: "Certification",
    uploadDate: "2024-04-08",
    status: "verified",
    fileUrl: "/documents/indigency.pdf"
  }
];

export default function ApplicationDetail({ params }: { params: { id: string } }) {
  const applicationId = params.id;
  const [activeTab, setActiveTab] = useState("details");
  const [processingNote, setProcessingNote] = useState("");
  
  // In a real application, you would fetch the data based on the ID
  // Here we're just using dummy data
  const application = dummyApplication;
  const client = dummyClient;
  const documents = dummyDocuments;
  
  const breadcrumbs: BreadcrumbItem[] = [
    {
      title: "Admin",
      href: "/admin/dashboard",
    },
    {
      title: "Applications",
      href: "/admin/applications",
    },
    {
      title: `Application #${applicationId}`,
      href: `/admin/applications/${applicationId}`,
    },
  ];

  const getStatusBadge = (status: ApplicationDetail["status"]) => {
    const variants: Record<ApplicationDetail["status"], "default" | "secondary" | "destructive" | "outline" | "success"> = {
      pending: "outline",
      in_review: "default",
      approved: "success",
      rejected: "destructive",
      completed: "secondary",
    };
    const statusDisplay = status.replace('_', ' ');
    return <Badge variant={variants[status] as any}>{statusDisplay}</Badge>;
  };

  const getPriorityBadge = (priority: ApplicationDetail["priority"]) => {
    const variants: Record<ApplicationDetail["priority"], "default" | "secondary" | "destructive" | "outline"> = {
      normal: "outline",
      urgent: "default",
      critical: "destructive",
    };
    return <Badge variant={variants[priority]}>{priority}</Badge>;
  };

  const getDocumentStatusBadge = (status: Document["status"]) => {
    const variants: Record<Document["status"], "default" | "secondary" | "destructive" | "outline"> = {
      verified: "secondary",
      pending: "default",
      rejected: "destructive",
    };
    return <Badge variant={variants[status]}>{status}</Badge>;
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-PH', {
      style: 'currency',
      currency: 'PHP'
    }).format(amount);
  };

  return (
    <AppLayout breadcrumbs={breadcrumbs}>
      <Head title={`Application #${applicationId}`} />

      <div className="container py-6">
        <div className="flex justify-between items-center mb-6">
          <div>
            <div className="flex items-center gap-4">
              <Button variant="outline" size="sm" asChild>
                <a href="/admin/applications">
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Back to Applications
                </a>
              </Button>
              {getStatusBadge(application.status)}
              {getPriorityBadge(application.priority)}
            </div>
            <h1 className="text-2xl font-bold mt-2">Application #{application.id}</h1>
            <p className="text-gray-500">{application.service}</p>
          </div>
          <div className="flex gap-2">
            {application.status === "in_review" && (
              <>
                <Button variant="default">
                  <CheckCircle className="h-4 w-4 mr-2" />
                  Approve
                </Button>
                <Button variant="destructive">
                  <XCircle className="h-4 w-4 mr-2" />
                  Reject
                </Button>
              </>
            )}
            {application.status === "approved" && (
              <Button variant="secondary">
                <CheckCircle className="h-4 w-4 mr-2" />
                Mark Complete
              </Button>
            )}
            {application.status === "pending" && (
              <Button variant="default">
                <CheckCircle className="h-4 w-4 mr-2" />
                Start Review
              </Button>
            )}
          </div>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="mb-6">
          <TabsList className="grid grid-cols-2 md:grid-cols-3 w-full">
            <TabsTrigger value="details">Application Details</TabsTrigger>
            <TabsTrigger value="documents">Documents</TabsTrigger>
            <TabsTrigger value="timeline">Timeline</TabsTrigger>
          </TabsList>

          <TabsContent value="details" className="mt-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <Card className="md:col-span-2">
                <CardHeader>
                  <CardTitle>Application Information</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-6">
                    <div>
                      <h3 className="text-sm font-medium text-gray-500 mb-2">Service Type</h3>
                      <p className="text-lg font-medium">{application.service}</p>
                    </div>
                    
                    <div>
                      <h3 className="text-sm font-medium text-gray-500 mb-2">Description</h3>
                      <p className="text-sm bg-gray-50 p-3 rounded">{application.description}</p>
                    </div>
                    
                    {application.requestedAmount && (
                      <div>
                        <h3 className="text-sm font-medium text-gray-500 mb-2">Financial Details</h3>
                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <p className="text-sm text-gray-500">Requested Amount</p>
                            <p className="text-lg font-medium">{formatCurrency(application.requestedAmount)}</p>
                          </div>
                          {application.approvedAmount && (
                            <div>
                              <p className="text-sm text-gray-500">Approved Amount</p>
                              <p className="text-lg font-medium">{formatCurrency(application.approvedAmount)}</p>
                            </div>
                          )}
                        </div>
                      </div>
                    )}
                    
                    <div>
                      <h3 className="text-sm font-medium text-gray-500 mb-2">Application Timeline</h3>
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <p className="text-sm text-gray-500">Submission Date</p>
                          <p className="text-sm">{application.submittedDate}</p>
                        </div>
                        <div>
                          <p className="text-sm text-gray-500">Last Updated</p>
                          <p className="text-sm">{application.lastUpdated}</p>
                        </div>
                      </div>
                    </div>
                    
                    {application.assignedTo && (
                      <div>
                        <h3 className="text-sm font-medium text-gray-500 mb-2">Assignment</h3>
                        <p className="text-sm">Assigned to: <span className="font-medium">{application.assignedTo}</span></p>
                      </div>
                    )}
                    
                    <div>
                      <h3 className="text-sm font-medium text-gray-500 mb-2">Notes</h3>
                      <div className="space-y-2">
                        {application.notes.map((note, index) => (
                          <div key={index} className="text-sm p-2 bg-gray-50 rounded">
                            {note}
                          </div>
                        ))}
                      </div>
                    </div>
                    
                    <Separator />
                    
                    <div>
                      <h3 className="text-sm font-medium mb-2">Add Processing Note</h3>
                      <Textarea
                        value={processingNote}
                        onChange={(e) => setProcessingNote(e.target.value)}
                        placeholder="Add notes about processing this application..."
                        className="mb-2"
                      />
                      <Button size="sm">
                        <MessageSquare className="h-4 w-4 mr-2" />
                        Add Note
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <div className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Client Information</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="flex items-center gap-3">
                        <User className="h-10 w-10 p-2 bg-gray-100 rounded-full text-gray-600" />
                        <div>
                          <a 
                            href={`/admin/clients/${client.id}`}
                            className="font-medium hover:underline text-blue-600"
                          >
                            {client.name}
                          </a>
                          <Badge 
                            variant={client.status === "verified" ? "secondary" : "outline"}
                            className="ml-2"
                          >
                            {client.status}
                          </Badge>
                        </div>
                      </div>
                      
                      <div className="space-y-3 text-sm">
                        <div className="flex items-center gap-2">
                          <Mail className="h-4 w-4 text-gray-400" />
                          <span>{client.email}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Phone className="h-4 w-4 text-gray-400" />
                          <span>{client.phone}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Home className="h-4 w-4 text-gray-400" />
                          <div>
                            <div>{client.address}</div>
                            <div className="text-gray-500">Barangay: {client.barangay}</div>
                          </div>
                        </div>
                      </div>
                      
                      <div className="pt-2">
                        <Button variant="outline" size="sm" className="w-full" asChild>
                          <a href={`/admin/clients/${client.id}`}>
                            <User className="h-4 w-4 mr-2" />
                            View Full Profile
                          </a>
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
                
                <Card>
                  <CardHeader>
                    <CardTitle>Quick Actions</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      <Button variant="outline" className="w-full justify-start" asChild>
                        <a href="#documents">
                          <FileText className="h-4 w-4 mr-2" />
                          View Documents
                        </a>
                      </Button>
                      <Button variant="outline" className="w-full justify-start">
                        <Download className="h-4 w-4 mr-2" />
                        Export Application
                      </Button>
                      <Button variant="outline" className="w-full justify-start">
                        <MessageSquare className="h-4 w-4 mr-2" />
                        Contact Client
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="documents" className="mt-6">
            <Card>
              <CardHeader>
                <div className="flex justify-between items-center">
                  <CardTitle>Supporting Documents</CardTitle>
                  <Button>
                    <FileText className="h-4 w-4 mr-2" />
                    Upload Document
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                {documents.length > 0 ? (
                  <div className="space-y-4">
                    {documents.map((doc) => (
                      <div key={doc.id} className="p-4 border rounded-md">
                        <div className="flex justify-between items-start">
                          <div>
                            <div className="flex items-center gap-2 mb-1">
                              <h3 className="font-medium">{doc.name}</h3>
                              {getDocumentStatusBadge(doc.status)}
                            </div>
                            <p className="text-sm text-gray-500">{doc.type}</p>
                            <div className="text-sm text-gray-500 mt-1">
                              <div className="flex items-center gap-1">
                                <Calendar className="h-4 w-4" />
                                Uploaded: {doc.uploadDate}
                              </div>
                            </div>
                          </div>
                          <div className="flex gap-2">
                            <Button variant="outline" size="sm" asChild>
                              <a href={doc.fileUrl} target="_blank" rel="noopener noreferrer">
                                <FileText className="h-4 w-4 mr-2" />
                                View
                              </a>
                            </Button>
                            {doc.status !== "verified" && (
                              <Button size="sm" variant="default">
                                <CheckCircle className="h-4 w-4 mr-2" />
                                Verify
                              </Button>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center p-6 text-gray-500">
                    <FileText className="h-12 w-12 mx-auto mb-4" />
                    <h3 className="text-lg font-semibold">No documents attached</h3>
                    <p>This application doesn't have any supporting documents.</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="timeline" className="mt-6">
            <Card>
              <CardHeader>
                <CardTitle>Application Timeline</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  {application.timeline.map((event, index) => (
                    <div key={index} className="flex gap-4">
                      <div className="relative flex flex-col items-center">
                        <div className="flex h-8 w-8 items-center justify-center rounded-full bg-blue-100 text-blue-600">
                          <Clock className="h-4 w-4" />
                        </div>
                        {index < application.timeline.length - 1 && (
                          <div className="h-full w-0.5 bg-gray-200" />
                        )}
                      </div>
                      <div className="flex flex-col pb-6">
                        <div className="flex items-center gap-2">
                          <h3 className="font-medium">{event.status}</h3>
                          <span className="text-sm text-gray-500">{event.date}</span>
                        </div>
                        <p className="text-sm mt-1">{event.description}</p>
                        <p className="text-xs text-gray-500 mt-1">By: {event.actor}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </AppLayout>
  );
} 