interface Service {
  id: number
  title: string
  description: string
  eligibility: string[]
  requirements: string[]
  process: string[]
  status: "available" | "limited" | "unavailable"
  schedule: string
  location: string
  maxBeneficiaries?: number
  currentBeneficiaries?: number
}

interface UserApplication {
  serviceId: number
  status: "pending" | "approved" | "rejected"
  appliedAt: string
  rejectionReason?: string | null
}

interface Appointment {
  id: number
  socialWorker: {
    id: number
    name: string
    email: string
  }
  date: string
  time: string
  location: string
  purpose: string
  status: "scheduled" | "completed" | "cancelled" | "rescheduled"
  notes?: string
  rescheduledTo?: {
    date: string
    time: string
  }
}

// Dummy Services Data
export const dummyServices: Service[] = [
  {
    id: 1,
    title: "Medical Assistance Program",
    description: "Financial assistance for medical expenses, including hospitalization, medicines, and laboratory tests.",
    eligibility: [
      "Resident of Balagtas",
      "Monthly income below ₱15,000",
      "No existing medical insurance",
      "Has valid medical prescription or hospital bills"
    ],
    requirements: [
      "Valid ID with Balagtas address",
      "Barangay Certificate of Residency",
      "Medical Abstract or Prescription",
      "Hospital Bills (if applicable)",
      "Certificate of Indigency"
    ],
    process: [
      "Submit complete requirements to the Social Services Office",
      "Undergo initial assessment with social worker",
      "Wait for verification of documents (2-3 business days)",
      "Attend final interview if approved",
      "Receive assistance within 5 business days after approval"
    ],
    status: "available",
    schedule: "Monday to Friday, 8:00 AM - 5:00 PM",
    location: "Balagtas Municipal Hall, 2nd Floor",
    maxBeneficiaries: 100,
    currentBeneficiaries: 45
  },
  {
    id: 2,
    title: "Educational Assistance",
    description: "Support for students including school supplies, uniforms, and other educational expenses.",
    eligibility: [
      "Resident of Balagtas",
      "Currently enrolled student",
      "Family income below poverty threshold",
      "Maintaining grades of at least 80%"
    ],
    requirements: [
      "School Registration Form",
      "Report Card",
      "Certificate of Enrollment",
      "Parents' Income Certificate",
      "Barangay Certificate"
    ],
    process: [
      "Submit requirements to Education Assistance Office",
      "Attend family assessment interview",
      "Submit additional documents if requested",
      "Receive notification of approval/rejection",
      "Claim assistance package"
    ],
    status: "limited",
    schedule: "Monday to Thursday, 9:00 AM - 3:00 PM",
    location: "Balagtas Municipal Hall, 3rd Floor",
    maxBeneficiaries: 200,
    currentBeneficiaries: 180
  },
  {
    id: 3,
    title: "Senior Citizen Benefits",
    description: "Various benefits and assistance programs specifically for senior citizens of Balagtas.",
    eligibility: [
      "Resident of Balagtas",
      "60 years old and above",
      "Registered in the Senior Citizens' Office",
      "With valid Senior Citizen ID"
    ],
    requirements: [
      "Senior Citizen ID",
      "Proof of Residency",
      "Latest ID Picture",
      "Birth Certificate"
    ],
    process: [
      "Register at the Senior Citizens' Office",
      "Submit complete requirements",
      "Undergo verification process",
      "Attend orientation",
      "Regular monthly claiming of benefits"
    ],
    status: "available",
    schedule: "Tuesday to Friday, 8:00 AM - 4:00 PM",
    location: "Balagtas Senior Citizens' Center",
    maxBeneficiaries: 500,
    currentBeneficiaries: 320
  }
];

export const dummyUserApplications: UserApplication[] = [
  {
    serviceId: 1,
    status: "approved",
    appliedAt: "2024-03-15T08:30:00",
    rejectionReason: null
  },
  {
    serviceId: 2,
    status: "pending",
    appliedAt: "2024-04-01T14:45:00",
    rejectionReason: null
  }
];

// Dummy Appointments Data
export const dummyAppointments: {
  upcoming: Appointment[];
  past: Appointment[];
} = {
  upcoming: [
    {
      id: 1,
      socialWorker: {
        id: 101,
        name: "Maria Santos",
        email: "<EMAIL>"
      },
      date: "2024-04-15",
      time: "10:30 AM",
      location: "Social Services Office, Room 201",
      purpose: "Medical Assistance Application Review",
      status: "scheduled",
      notes: "Please bring all required medical documents and valid ID"
    },
    {
      id: 2,
      socialWorker: {
        id: 102,
        name: "Juan Dela Cruz",
        email: "<EMAIL>"
      },
      date: "2024-04-20",
      time: "2:00 PM",
      location: "Municipal Hall, Conference Room A",
      purpose: "Educational Assistance Interview",
      status: "scheduled",
      notes: "Bring student's report card and proof of enrollment"
    }
  ],
  past: [
    {
      id: 3,
      socialWorker: {
        id: 103,
        name: "Ana Reyes",
        email: "<EMAIL>"
      },
      date: "2024-03-10",
      time: "9:00 AM",
      location: "Social Services Office, Room 203",
      purpose: "Senior Citizen Benefits Registration",
      status: "completed",
      notes: "Successfully registered for senior benefits program"
    },
    {
      id: 4,
      socialWorker: {
        id: 101,
        name: "Maria Santos",
        email: "<EMAIL>"
      },
      date: "2024-03-05",
      time: "11:00 AM",
      location: "Social Services Office, Room 201",
      purpose: "Initial Consultation",
      status: "rescheduled",
      notes: "Rescheduled due to emergency",
      rescheduledTo: {
        date: "2024-04-15",
        time: "10:30 AM"
      }
    }
  ]
}; 