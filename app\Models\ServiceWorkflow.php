<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ServiceWorkflow extends Model
{
    use SoftDeletes;

    protected $fillable = [
        'service_id',
        'stage_name',
        'description',
        'order',
        'role_required',
        'sla_hours',
        'actions_required',
        'notification_rules',
        'escalation_rules',
        'requires_approval',
        'is_active',
    ];

    protected $casts = [
        'order' => 'integer',
        'sla_hours' => 'integer',
        'actions_required' => 'array',
        'notification_rules' => 'array',
        'escalation_rules' => 'array',
        'requires_approval' => 'boolean',
        'is_active' => 'boolean',
    ];

    /**
     * Get the service that owns the workflow stage.
     */
    public function service(): BelongsTo
    {
        return $this->belongsTo(Service::class);
    }
}
