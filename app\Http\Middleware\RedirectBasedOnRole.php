<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class RedirectBasedOnRole
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        if (!Auth::check()) {
            return redirect('/login');
        }

        $user = Auth::user();
        $intendedUrl = $request->session()->get('url.intended');
        $currentPath = $request->path();
        
        // Debug info
        \Log::info('User accessing path', [
            'user' => $user->name,
            'role' => $user->role,
            'path' => $currentPath
        ]);

        // Admin routes should only be accessible by admin users
        if (strpos($currentPath, 'admin/') === 0 && $user->role !== 'admin') {
            \Log::warning('Non-admin trying to access admin route', [
                'user' => $user->name,
                'role' => $user->role,
                'path' => $currentPath
            ]);
            
            // Redirect based on user's role
            switch ($user->role) {
                case 'superadmin':
                    return redirect(route('superadmin.dashboard'));
                case 'client':
                    return redirect(route('dashboard'));
                default:
                    return redirect(route('login'));
            }
        }

        // Superadmin routes should only be accessible by superadmin users
        if (strpos($currentPath, 'superadmin/') === 0 && $user->role !== 'superadmin') {
            \Log::warning('Non-superadmin trying to access superadmin route', [
                'user' => $user->name,
                'role' => $user->role,
                'path' => $currentPath
            ]);
            
            // Redirect based on user's role
            switch ($user->role) {
                case 'admin':
                    return redirect(route('admin.dashboard'));
                case 'client':
                    return redirect(route('dashboard'));
                default:
                    return redirect(route('login'));
            }
        }

        // Client routes should be accessible by all authenticated users
        // but admin and superadmin users should be redirected to their respective dashboards
        // if they try to access the client dashboard
        if ($currentPath === 'dashboard' && $user->role !== 'client') {
            switch ($user->role) {
                case 'superadmin':
                    return redirect(route('superadmin.dashboard'));
                case 'admin':
                    return redirect(route('admin.dashboard'));
                default:
                    // Allow access
                    break;
            }
        }

        // If there's no intended URL or the intended URL is the home page
        if (!$intendedUrl || $intendedUrl === '/' || $intendedUrl === route('home')) {
            switch ($user->role) {
                case 'superadmin':
                    return redirect(route('superadmin.dashboard'));
                case 'admin':
                    return redirect(route('admin.dashboard'));
                case 'client':
                    return redirect(route('dashboard'));
                default:
                    return redirect(route('login'));
            }
        }

        return $next($request);
    }
}
