<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('service_workflows', function (Blueprint $table) {
            $table->id();
            $table->foreignId('service_id')->constrained()->onDelete('cascade');
            $table->string('stage_name');
            $table->text('description')->nullable();
            $table->integer('order');
            $table->string('role_required'); // admin, superadmin, etc.
            $table->integer('sla_hours')->default(24); // Service Level Agreement in hours
            $table->json('actions_required')->nullable();
            $table->json('notification_rules')->nullable();
            $table->json('escalation_rules')->nullable();
            $table->boolean('requires_approval')->default(true);
            $table->boolean('is_active')->default(true);
            $table->timestamps();
            $table->softDeletes();

            $table->unique(['service_id', 'order']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('service_workflows');
    }
};
