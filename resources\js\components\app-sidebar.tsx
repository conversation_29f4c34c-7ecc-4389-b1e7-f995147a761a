import { NavFooter } from '@/components/nav-footer';
import { NavMain } from '@/components/nav-main';
import { NavUser } from '@/components/nav-user';
import { Separator } from '@/components/ui/separator';
import { Sidebar, SidebarContent, SidebarFooter, SidebarHeader, SidebarMenu, SidebarMenuButton, SidebarMenuItem } from '@/components/ui/sidebar';
import { type NavItem } from '@/types';
import { Link, usePage } from '@inertiajs/react';
import { 
    BookOpen, 
    ClipboardCheck, 
    FileCheck, 
    FileText, 
    LayoutGrid, 
    ShieldCheck, 
    UserCheck, 
    Users, 
    BarChart, 
    CalendarDays, 
    CheckCheck, 
    Settings, 
    Database, 
    ServerCog,
    CreditCard,
    Box
} from 'lucide-react';
import AppLogo from './app-logo';

interface PageProps {
    auth?: {
        user?: {
            role: string;
        };
    };
}

// Client navigation items
const clientNavItems: NavItem[] = [
    {
        title: 'Dashboard',
        href: '/dashboard',
        icon: LayoutGrid,
    },
    {
        title: 'Residency Verification',
        href: '/residency-verification',
        icon: UserCheck,
    },
    {
        title: 'BFACES Application',
        href: '/bfaces-application',
        icon: FileText,
    },
    {
        title: 'Services',
        href: '/services',
        icon: ClipboardCheck,
    },
    {
        title: 'Applications',
        href: '/applications',
        icon: FileCheck,
    },
    {
        title: 'Appointments',
        href: '/appointments',
        icon: CalendarDays,
    },
];

// Admin (Social Worker) navigation items
const adminNavItems: NavItem[] = [
    {
        title: 'Dashboard',
        href: '/admin/dashboard',
        icon: LayoutGrid,
    },
    {
        title: 'Verifications',
        href: '/admin/verifications',
        icon: CheckCheck,
    },
    {
        title: 'Applications',
        href: '/admin/applications',
        icon: FileCheck,
    },
    {
        title: 'Interviews',
        href: '/admin/interviews',
        icon: CalendarDays,
    },
    {
        title: 'Clients',
        href: '/admin/clients',
        icon: Users,
    },
    {
        title: 'Reports',
        href: '/admin/reports',
        icon: BarChart,
    },
];

// Super Admin navigation items
const superAdminNavItems: NavItem[] = [
    {
        title: 'Dashboard',
        href: '/superadmin/dashboard',
        icon: LayoutGrid,
    },
    {
        title: 'System Configuration',
        href: '/superadmin/configuration',
        icon: Settings,
    },
    {
        title: 'Service Management',
        href: '/superadmin/services',
        icon: Box,
    },
    {
        title: 'User Management',
        href: '/superadmin/users',
        icon: Users,
    },
    {
        title: 'Budget Allocation',
        href: '/superadmin/budget',
        icon: CreditCard,
    },
    {
        title: 'Database Management',
        href: '/superadmin/database',
        icon: Database,
    },
    {
        title: 'Reports & Analytics',
        href: '/superadmin/reports',
        icon: BarChart,
    },
];

const footerNavItems: NavItem[] = [
    
];

export function AppSidebar() {
    const { auth } = usePage().props as PageProps;
    const userRole = auth?.user?.role || 'client';

    const getNavItems = () => {
        switch (userRole) {
            case 'admin':
                return adminNavItems;
            case 'superadmin':
                return superAdminNavItems;
            default:
                return clientNavItems;
        }
    };

    return (
        <Sidebar collapsible="icon" variant="inset">
            <SidebarHeader>
                <SidebarMenu>
                    <SidebarMenuItem>
                        <SidebarMenuButton size="lg" asChild>
                            <Link href="/dashboard" prefetch>
                                <AppLogo />
                            </Link>
                        </SidebarMenuButton>
                    </SidebarMenuItem>
                </SidebarMenu>
            </SidebarHeader>

            <SidebarContent>
                <NavMain items={getNavItems()} />
            </SidebarContent>

            <SidebarFooter>
                <NavFooter items={footerNavItems} className="mt-auto" />
                <NavUser />
            </SidebarFooter>
        </Sidebar>
    );
}
