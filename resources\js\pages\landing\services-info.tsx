import React, { useState, useEffect } from 'react';
import { Head, usePage } from '@inertiajs/react';
import { type SharedData } from '@/types';
import LandingLayout from '@/components/landing-layout';
import LandingNav from '@/components/landing-nav';
import { Card, CardContent } from '@/components/ui/card';
import Skeleton from 'react-loading-skeleton';
import 'react-loading-skeleton/dist/skeleton.css';
import { 
    HeartHandshakeIcon, 
    ShieldIcon, 
    UsersIcon, 
    FileTextIcon,
    HeartIcon,
    FileCheckIcon
} from 'lucide-react';

// Skeleton components
const ServiceCardSkeleton = () => (
    <Card className="border-purple-100">
        <CardContent className="p-6">
            <div className="flex items-start gap-4">
                <Skeleton circle width={56} height={56} className="shrink-0" />
                <div className="w-full">
                    <Skeleton width={200} height={24} className="mb-2" />
                    <Skeleton count={2} className="mb-4" />
                    <Skeleton width={120} height={20} className="mb-2" />
                    <div className="grid grid-cols-2 gap-y-1 gap-x-4">
                        {[1, 2, 3, 4].map((i) => (
                            <Skeleton key={i} width={120} />
                        ))}
                    </div>
                </div>
            </div>
        </CardContent>
    </Card>
);

const ProcessStepSkeleton = () => (
    <li className="flex items-start gap-3">
        <Skeleton circle width={24} height={24} className="shrink-0" />
        <div className="w-full">
            <Skeleton width={200} height={20} className="mb-1" />
            <Skeleton width={300} />
        </div>
    </li>
);

export default function ServicesInfo() {
    const { auth } = usePage<SharedData>().props;
    const [isLoading, setIsLoading] = useState(true);

    useEffect(() => {
        const timer = setTimeout(() => {
            setIsLoading(false);
        }, 1000);

        return () => clearTimeout(timer);
    }, []);

    const services = [
        {
            title: "Medical Assistance",
            description: "Financial assistance for medical expenses, hospitalization, and medicine purchase for residents experiencing health crises.",
            icon: <HeartIcon className="h-8 w-8 text-purple-600" />,
            benefits: [
                "Hospital bill assistance", 
                "Medication subsidies", 
                "Laboratory fee coverage", 
                "Medical consultation support"
            ]
        },
        {
            title: "Burial Assistance",
            description: "Financial aid to help families cover funeral and burial expenses for deceased family members during difficult times.",
            icon: <HeartHandshakeIcon className="h-8 w-8 text-purple-600" />,
            benefits: [
                "Casket provision", 
                "Funeral service support", 
                "Transportation assistance", 
                "Processing of death certificates"
            ]
        },
        {
            title: "Aid to Senior Citizens",
            description: "Support programs specifically designed for elderly residents of Balagtas, focusing on their unique needs and challenges.",
            icon: <ShieldIcon className="h-8 w-8 text-purple-600" />,
            benefits: [
                "Financial assistance", 
                "Healthcare support", 
                "Medication subsidies", 
                "Social pension processing"
            ]
        },
        {
            title: "PAO Certification",
            description: "Official certification required for residents seeking legal aid services through the Public Attorney's Office.",
            icon: <FileTextIcon className="h-8 w-8 text-purple-600" />,
            benefits: [
                "Indigency verification", 
                "Legal aid qualification", 
                "Court fee waivers", 
                "Access to public attorneys"
            ]
        },
        {
            title: "PhilHealth Certification",
            description: "Assistance with PhilHealth-related documentation and certification needed to access healthcare benefits for qualified residents.",
            icon: <FileCheckIcon className="h-8 w-8 text-purple-600" />,
            benefits: [
                "PhilHealth membership processing", 
                "Premium subsidies for indigents", 
                "Benefit verification", 
                "Claims assistance"
            ]
        }
    ];

    return (
        <LandingLayout title="Our Services">
            <Head title="Services - Balagtas Social Care" />
            
            <div className="container mx-auto px-4 py-12">
                {/* Introduction */}
                <div className="max-w-3xl mx-auto mb-16 text-center">
                    {isLoading ? (
                        <>
                            <Skeleton height={40} width={300} className="mb-4" />
                            <Skeleton count={2} />
                        </>
                    ) : (
                        <>
                            <h1 className="text-3xl font-bold text-purple-900 mb-4">Our Services</h1>
                            <p className="text-lg text-gray-700">
                            Balagtas Social Care provides comprehensive social welfare services for community members in need, focusing on crisis and emergency situations through our BFACES program. Verified residents can access one service at a time with a 12-month cooldown period between claims.
                            </p>
                        </>
                    )}
                </div>

                <div className="grid md:grid-cols-2 gap-8">
                    {isLoading ? (
                        Array(5).fill(null).map((_, index) => (
                            <ServiceCardSkeleton key={index} />
                        ))
                    ) : (
                        services.map((service, index) => (
                            <Card key={index} className="border-purple-100 hover:shadow-md transition-shadow">
                                <CardContent className="p-6">
                                    <div className="flex items-start gap-4">
                                        <div className="bg-purple-100 p-3 rounded-full flex items-center justify-center shrink-0 mt-1">
                                            {service.icon}
                                        </div>
                                        <div>
                                            <h3 className="text-xl font-semibold mb-2">{service.title}</h3>
                                            <p className="text-gray-600 mb-4">{service.description}</p>
                                            
                                            <h4 className="font-medium text-purple-700 mb-2">Key Benefits:</h4>
                                            <ul className="grid grid-cols-2 gap-y-1 gap-x-4">
                                                {service.benefits.map((benefit, idx) => (
                                                    <li key={idx} className="text-sm text-gray-600 flex items-center">
                                                        <span className="bg-purple-600 rounded-full h-1.5 w-1.5 mr-2"></span>
                                                        {benefit}
                                                    </li>
                                                ))}
                                            </ul>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>
                        ))
                    )}
                </div>

                <div className="bg-purple-100 rounded-lg p-8 mt-12 max-w-4xl mx-auto">
                    <h2 className="text-2xl font-bold text-purple-800 mb-4">Service Availment Process</h2>
                    <p className="text-gray-700 mb-4">
                        To access any of our services, you'll need to follow our layered process:
                    </p>
                    <ol className="space-y-4 mb-6">
                        <li className="flex items-start gap-3">
                            <div className="bg-purple-600 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm shrink-0 mt-0.5">1</div>
                            <div>
                                <p className="font-medium">Register an account on the Balagtas Social Care platform</p>
                                <p className="text-sm text-gray-600">Create your personal profile with complete information</p>
                            </div>
                        </li>
                        <li className="flex items-start gap-3">
                            <div className="bg-purple-600 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm shrink-0 mt-0.5">2</div>
                            <div>
                                <p className="font-medium">Complete the residency verification process</p>
                                <p className="text-sm text-gray-600">Upload proof of residency such as utility bills or government ID</p>
                            </div>
                        </li>
                        <li className="flex items-start gap-3">
                            <div className="bg-purple-600 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm shrink-0 mt-0.5">3</div>
                            <div>
                                <p className="font-medium">Apply for BFACES</p>
                                <p className="text-sm text-gray-600">Fill out the BFACES form with household information and crisis details</p>
                            </div>
                        </li>
                        <li className="flex items-start gap-3">
                            <div className="bg-purple-600 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm shrink-0 mt-0.5">4</div>
                            <div>
                                <p className="font-medium">Select a specific service</p>
                                <p className="text-sm text-gray-600">Once BFACES is verified, you can apply for specific services</p>
                            </div>
                        </li>
                        <li className="flex items-start gap-3">
                            <div className="bg-purple-600 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm shrink-0 mt-0.5">5</div>
                            <div>
                                <p className="font-medium">Document verification and interview</p>
                                <p className="text-sm text-gray-600">Upload documents online, then submit physical copies and complete an interview</p>
                            </div>
                        </li>
                        <li className="flex items-start gap-3">
                            <div className="bg-purple-600 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm shrink-0 mt-0.5">6</div>
                            <div>
                                <p className="font-medium">Claim your assistance</p>
                                <p className="text-sm text-gray-600">Within 24 hours of your interview, your application will be processed for claiming</p>
                            </div>
                        </li>
                    </ol>
                    <div className="bg-purple-50 p-4 rounded-lg border border-purple-200">
                        <h4 className="font-medium text-purple-800 mb-2">Important Service Rules:</h4>
                        <ul className="text-sm text-gray-700 space-y-2">
                            <li>• After claiming assistance for a specific service, you will be placed under a 12-month cooldown period 
                                for that particular service.</li>
                            <li>• You cannot avail services simultaneously - only one service application can be active at a time.</li>
                            <li>• You must complete or withdraw your current service application before applying for another service.</li>
                            <li>• These policies ensure that resources are distributed equitably among all Balagtas residents.</li>
                        </ul>
                    </div>
                </div>
            </div>
        </LandingLayout>
    );
} 