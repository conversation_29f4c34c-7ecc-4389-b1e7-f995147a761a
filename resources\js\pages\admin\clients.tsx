import { Head } from "@inertiajs/react";
import AppLayout from "@/layouts/app/app-sidebar-layout";
import { Card } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Search, User, Calendar, FileText, Phone, Home, Mail, Edit, Eye, MoreHorizontal, History, AlertCircle, CheckCircle } from "lucide-react";
import { useState } from "react";
import { type BreadcrumbItem } from "@/types";

interface Client {
  id: string;
  name: string;
  email: string;
  phone: string;
  barangay: string;
  address: string;
  status: "verified" | "pending" | "inactive" | "blocked";
  joinedDate: string;
  lastActive: string;
  applications: number;
  servicesReceived: number;
  pendingVerifications: number;
  bfacesStatus: "verified" | "pending" | "none";
}

// Dummy data for demonstration
const dummyClients: Client[] = [
  {
    id: "1",
    name: "Juan Dela Cruz",
    email: "<EMAIL>",
    phone: "09123456789",
    barangay: "San Miguel",
    address: "123 Main St., San Miguel, Balagtas",
    status: "verified",
    joinedDate: "2024-01-15",
    lastActive: "2024-04-08",
    applications: 3,
    servicesReceived: 2,
    pendingVerifications: 0,
    bfacesStatus: "verified"
  },
  {
    id: "2",
    name: "Maria Santos",
    email: "<EMAIL>",
    phone: "09234567890",
    barangay: "Wawa",
    address: "456 Secondary St., Wawa, Balagtas",
    status: "pending",
    joinedDate: "2024-03-20",
    lastActive: "2024-04-07",
    applications: 1,
    servicesReceived: 0,
    pendingVerifications: 1,
    bfacesStatus: "pending"
  },
  {
    id: "3",
    name: "Pedro Reyes",
    email: "<EMAIL>",
    phone: "09345678901",
    barangay: "Longos",
    address: "789 Third St., Longos, Balagtas",
    status: "inactive",
    joinedDate: "2023-11-10",
    lastActive: "2024-02-15",
    applications: 2,
    servicesReceived: 1,
    pendingVerifications: 0,
    bfacesStatus: "none"
  },
  {
    id: "4",
    name: "Juana Manalo",
    email: "<EMAIL>",
    phone: "09456789012",
    barangay: "Pinagbarilan",
    address: "101 Fourth St., Pinagbarilan, Balagtas",
    status: "verified",
    joinedDate: "2023-12-05",
    lastActive: "2024-04-06",
    applications: 5,
    servicesReceived: 3,
    pendingVerifications: 1,
    bfacesStatus: "verified"
  },
];

const breadcrumbs: BreadcrumbItem[] = [
  {
    title: "Admin",
    href: "/admin/dashboard",
  },
  {
    title: "Client Management",
    href: "/admin/clients",
  },
];

export default function Clients() {
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [barangayFilter, setBarangayFilter] = useState<string>("all");
  const [searchQuery, setSearchQuery] = useState("");
  const [activeTab, setActiveTab] = useState("all");

  const filteredClients = dummyClients.filter(client => {
    const matchesStatus = statusFilter === "all" || client.status === statusFilter;
    const matchesBarangay = barangayFilter === "all" || client.barangay === barangayFilter;
    const matchesSearch = 
      client.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      client.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
      client.phone.includes(searchQuery);
    
    // Filter based on active tab
    if (activeTab === "all") return matchesStatus && matchesBarangay && matchesSearch;
    if (activeTab === "verified") return client.status === "verified" && matchesBarangay && matchesSearch;
    if (activeTab === "pending") return client.status === "pending" && matchesBarangay && matchesSearch;
    if (activeTab === "inactive") return client.status === "inactive" && matchesBarangay && matchesSearch;
    
    return true;
  });

  const getStatusBadge = (status: Client["status"]) => {
    const variants: Record<Client["status"], "default" | "secondary" | "destructive" | "outline"> = {
      verified: "secondary",
      pending: "default",
      inactive: "outline",
      blocked: "destructive",
    };
    return <Badge variant={variants[status]}>{status}</Badge>;
  };

  const getBfacesBadge = (status: Client["bfacesStatus"]) => {
    const variants: Record<Client["bfacesStatus"], "default" | "secondary" | "destructive" | "outline"> = {
      verified: "secondary",
      pending: "default",
      none: "outline",
    };
    return <Badge variant={variants[status]}>BFACES: {status}</Badge>;
  };

  return (
    <AppLayout breadcrumbs={breadcrumbs}>
      <Head title="Client Management" />

      <div className="container py-6">
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-2xl font-bold">Client Management</h1>
            <p className="text-sm text-gray-500 mt-1">
              Manage and track client information and services
            </p>
          </div>
          <div className="flex gap-2">
            <Button>Export List</Button>
          </div>
        </div>

        <Card className="p-4 mb-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <Label htmlFor="search">Search</Label>
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  id="search"
                  placeholder="Search by name, email, phone..."
                  className="pl-8"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
            </div>
            <div>
              <Label htmlFor="status">Status</Label>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger id="status">
                  <SelectValue placeholder="Filter by status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="verified">Verified</SelectItem>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="inactive">Inactive</SelectItem>
                  <SelectItem value="blocked">Blocked</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="barangay">Barangay</Label>
              <Select value={barangayFilter} onValueChange={setBarangayFilter}>
                <SelectTrigger id="barangay">
                  <SelectValue placeholder="Filter by barangay" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Barangays</SelectItem>
                  <SelectItem value="San Miguel">San Miguel</SelectItem>
                  <SelectItem value="Wawa">Wawa</SelectItem>
                  <SelectItem value="Longos">Longos</SelectItem>
                  <SelectItem value="Pinagbarilan">Pinagbarilan</SelectItem>
                  <SelectItem value="Santol">Santol</SelectItem>
                  <SelectItem value="Borol 1st">Borol 1st</SelectItem>
                  <SelectItem value="Borol 2nd">Borol 2nd</SelectItem>
                  <SelectItem value="Panginay">Panginay</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </Card>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="mb-6">
          <TabsList>
            <TabsTrigger value="all">All Clients</TabsTrigger>
            <TabsTrigger value="verified">Verified</TabsTrigger>
            <TabsTrigger value="pending">Pending</TabsTrigger>
            <TabsTrigger value="inactive">Inactive</TabsTrigger>
          </TabsList>
        </Tabs>

        <div className="space-y-4">
          {filteredClients.map((client) => (
            <Card key={client.id} className="p-4">
              <div className="flex flex-col md:flex-row md:items-start justify-between gap-4">
                <div className="flex gap-4">
                  <div className="mt-1">
                    <User className="h-10 w-10 p-2 bg-gray-100 rounded-full text-gray-600" />
                  </div>
                  <div className="flex-1">
                    <div className="flex flex-col md:flex-row md:items-center gap-2 flex-wrap">
                      <h3 className="font-semibold text-lg">{client.name}</h3>
                      <div className="flex gap-2">
                        {getStatusBadge(client.status)}
                        {getBfacesBadge(client.bfacesStatus)}
                      </div>
                    </div>
                    <div className="mt-2 space-y-1 text-sm text-gray-500">
                      <div className="flex items-center gap-2">
                        <Mail className="h-4 w-4" />
                        <span>{client.email}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Phone className="h-4 w-4" />
                        <span>{client.phone}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Home className="h-4 w-4" />
                        <span>{client.address}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Calendar className="h-4 w-4" />
                        <span>Joined: {client.joinedDate}</span>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="flex flex-col gap-2">
                  <div className="grid grid-cols-3 gap-2 text-center">
                    <div className="bg-gray-50 p-2 rounded">
                      <div className="text-sm font-semibold">{client.applications}</div>
                      <div className="text-xs text-gray-500">Applications</div>
                    </div>
                    <div className="bg-gray-50 p-2 rounded">
                      <div className="text-sm font-semibold">{client.servicesReceived}</div>
                      <div className="text-xs text-gray-500">Services</div>
                    </div>
                    <div className="bg-gray-50 p-2 rounded">
                      <div className="text-sm font-semibold">{client.pendingVerifications}</div>
                      <div className="text-xs text-gray-500">Pending</div>
                    </div>
                  </div>
                  <div className="flex justify-end gap-2 mt-2">
                    <Button size="sm" variant="outline" asChild>
                      <a href={`/admin/clients/${client.id}`}>
                        <Eye className="h-4 w-4 mr-2" />
                        View
                      </a>
                    </Button>
                    <Button size="sm" variant="outline">
                      <History className="h-4 w-4 mr-2" />
                      History
                    </Button>
                    <Dialog>
                      <DialogTrigger asChild>
                        <Button size="sm" variant="outline">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DialogTrigger>
                      <DialogContent>
                        <DialogHeader>
                          <DialogTitle>Client Actions</DialogTitle>
                          <DialogDescription>
                            Choose an action for {client.name}
                          </DialogDescription>
                        </DialogHeader>
                        <div className="grid grid-cols-1 gap-2 py-4">
                          <Button variant="outline" className="justify-start">
                            <Edit className="h-4 w-4 mr-2" />
                            Edit Client Details
                          </Button>
                          <Button variant="outline" className="justify-start">
                            <FileText className="h-4 w-4 mr-2" />
                            View Documents
                          </Button>
                          {client.status === "pending" && (
                            <Button variant="default" className="justify-start">
                              <CheckCircle className="h-4 w-4 mr-2" />
                              Verify Client
                            </Button>
                          )}
                          {client.status === "inactive" && (
                            <Button variant="default" className="justify-start">
                              <CheckCircle className="h-4 w-4 mr-2" />
                              Reactivate Client
                            </Button>
                          )}
                          {client.status === "verified" && (
                            <Button variant="destructive" className="justify-start">
                              <AlertCircle className="h-4 w-4 mr-2" />
                              Flag Client
                            </Button>
                          )}
                        </div>
                        <DialogFooter>
                          <Button variant="outline">Cancel</Button>
                        </DialogFooter>
                      </DialogContent>
                    </Dialog>
                  </div>
                </div>
              </div>
            </Card>
          ))}

          {filteredClients.length === 0 && (
            <Card className="p-8">
              <div className="text-center text-gray-500">
                <User className="h-12 w-12 mx-auto mb-4" />
                <h3 className="text-lg font-semibold">No clients found</h3>
                <p>There are no clients matching your filters.</p>
              </div>
            </Card>
          )}
        </div>
      </div>
    </AppLayout>
  );
} 