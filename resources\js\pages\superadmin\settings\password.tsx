import { Head } from "@inertiajs/react"
import { <PERSON><PERSON>ontent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Button } from "@/components/ui/button"
import { useForm } from "@inertiajs/react"
import SettingsLayout from "@/layouts/settings/settings-layout"

export default function SuperadminPassword() {
  const { data, setData, patch, errors, processing, reset } = useForm({
    current_password: "",
    password: "",
    password_confirmation: "",
  })

  function submit(e: React.FormEvent) {
    e.preventDefault()
    patch(`/superadmin/settings/password`, {
      onSuccess: () => reset("password", "password_confirmation"),
    })
  }

  return (
    <SettingsLayout role="superadmin">
      <Head title="Change Password" />

      <form onSubmit={submit}>
        <CardHeader>
          <CardTitle>Change Password</CardTitle>
          <CardDescription>
            Ensure your account is using a secure password.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="current_password">Current Password</Label>
            <Input
              id="current_password"
              type="password"
              value={data.current_password}
              onChange={e => setData("current_password", e.target.value)}
            />
            {errors.current_password && (
              <p className="text-sm text-red-500">{errors.current_password}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="password">New Password</Label>
            <Input
              id="password"
              type="password"
              value={data.password}
              onChange={e => setData("password", e.target.value)}
            />
            {errors.password && (
              <p className="text-sm text-red-500">{errors.password}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="password_confirmation">Confirm Password</Label>
            <Input
              id="password_confirmation"
              type="password"
              value={data.password_confirmation}
              onChange={e => setData("password_confirmation", e.target.value)}
            />
            {errors.password_confirmation && (
              <p className="text-sm text-red-500">
                {errors.password_confirmation}
              </p>
            )}
          </div>

          <div className="flex justify-end">
            <Button type="submit" disabled={processing}>
              Save Changes
            </Button>
          </div>
        </CardContent>
      </form>
    </SettingsLayout>
  )
} 