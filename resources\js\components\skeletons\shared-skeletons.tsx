import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";

export const StatCardsSkeleton = () => (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {[...Array(4)].map((_, i) => (
            <Card key={i}>
                <CardHeader>
                    <Skeleton className="h-4 w-[120px]" />
                </CardHeader>
                <CardContent>
                    <Skeleton className="h-7 w-[70px] mb-1" />
                    <Skeleton className="h-4 w-[100px]" />
                </CardContent>
            </Card>
        ))}
    </div>
);

export const TableRowSkeleton = () => (
    <div className="flex items-center justify-between p-4">
        <div className="space-y-2">
            <Skeleton className="h-4 w-[150px]" />
            <Skeleton className="h-4 w-[100px]" />
        </div>
        <div className="space-y-2">
            <Skeleton className="h-4 w-[100px]" />
            <Skeleton className="h-4 w-[80px]" />
        </div>
    </div>
);

export const ChartSkeleton = () => (
    <div className="space-y-3">
        <Skeleton className="h-[400px] w-full" />
    </div>
);

export const FormSkeleton = () => (
    <div className="space-y-4">
        {[...Array(4)].map((_, i) => (
            <div key={i} className="space-y-2">
                <Skeleton className="h-4 w-[100px]" />
                <Skeleton className="h-10 w-full" />
            </div>
        ))}
    </div>
);

export const CardWithActionsSkeleton = () => (
    <Card>
        <CardHeader>
            <div className="flex justify-between">
                <Skeleton className="h-6 w-[200px]" />
                <div className="flex gap-2">
                    <Skeleton className="h-9 w-[100px]" />
                    <Skeleton className="h-9 w-[100px]" />
                </div>
            </div>
        </CardHeader>
        <CardContent>
            <div className="space-y-4">
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-[90%]" />
                <Skeleton className="h-4 w-[80%]" />
            </div>
        </CardContent>
    </Card>
);

export const TabsSkeleton = () => (
    <div className="space-y-6">
        <div className="flex gap-2">
            {[...Array(3)].map((_, i) => (
                <Skeleton key={i} className="h-10 w-[120px]" />
            ))}
        </div>
        <CardWithActionsSkeleton />
    </div>
); 