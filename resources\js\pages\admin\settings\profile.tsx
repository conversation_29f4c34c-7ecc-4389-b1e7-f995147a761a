import { type BreadcrumbItem } from '@/types';
import { Head, useForm } from '@inertiajs/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Button } from "@/components/ui/button"
import SettingsLayout from '@/layouts/settings-layout';
import { User2, Mail, Shield, BadgeCheck } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { type AuthUser } from '../tempData/types';

interface Props {
  user?: AuthUser;
}

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/admin/dashboard',
    },
    {
        title: 'Settings',
        href: '/admin/settings/profile',
    },
    {
        title: 'Profile',
        href: '/admin/settings/profile',
    },
];

export default function AdminProfile({ user }: Props) {
  if (!user) {
    return (
      <SettingsLayout breadcrumbs={breadcrumbs} role="admin" current="profile">
        <Head title="Profile Settings" />
        <div className="p-6">
          <Card>
            <CardContent className="py-10">
              <div className="text-center text-muted-foreground">
                Loading user data...
              </div>
            </CardContent>
          </Card>
        </div>
      </SettingsLayout>
    );
  }

  const { data, setData, patch, errors, processing } = useForm({
    name: user.name,
    email: user.email,
  });

  function submit(e: React.FormEvent) {
    e.preventDefault();
    patch('/admin/settings/profile');
  }

  const permissions = user.permissions || [];

  return (
    <SettingsLayout breadcrumbs={breadcrumbs} role="admin" current="profile">
      <Head title="Profile Settings" />

      <div className="space-y-6">
        <Card>
          <form onSubmit={submit}>
            <CardHeader>
              <div className="flex items-center gap-4 mb-4">
                <div className="h-16 w-16 rounded-full bg-purple-100 flex items-center justify-center">
                  <User2 className="h-8 w-8 text-purple-600" />
                </div>
                <div>
                  <CardTitle>Profile Information</CardTitle>
                  <CardDescription>
                    Update your account information and email address.
                  </CardDescription>
                </div>
              </div>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid gap-6 md:grid-cols-2">
                <div>
                  <Label htmlFor="name" className="text-purple-900 mb-1.5 block">Full Name</Label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <User2 className="h-5 w-5 text-purple-400" />
                    </div>
                    <Input
                      id="name"
                      type="text"
                      value={data.name}
                      onChange={e => setData("name", e.target.value)}
                      className="pl-10 bg-white border-purple-300 focus:border-purple-500 focus:ring-purple-500 rounded-lg"
                      placeholder="Enter your name"
                    />
                  </div>
                  {errors.name && (
                    <p className="text-sm text-red-500 mt-1">{errors.name}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="email" className="text-purple-900 mb-1.5 block">Email Address</Label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <Mail className="h-5 w-5 text-purple-400" />
                    </div>
                    <Input
                      id="email"
                      type="email"
                      value={data.email}
                      onChange={e => setData("email", e.target.value)}
                      className="pl-10 bg-white border-purple-300 focus:border-purple-500 focus:ring-purple-500 rounded-lg"
                      placeholder="<EMAIL>"
                    />
                  </div>
                  {errors.email && (
                    <p className="text-sm text-red-500 mt-1">{errors.email}</p>
                  )}
                </div>
              </div>

              <div className="flex justify-end">
                <Button 
                  type="submit" 
                  disabled={processing}
                  className="bg-purple-600 text-white hover:bg-purple-700"
                >
                  Save Changes
                </Button>
              </div>
            </CardContent>
          </form>
        </Card>

        <Card>
          <CardHeader>
            <div className="flex items-center gap-4">
              <div className="h-16 w-16 rounded-full bg-green-100 flex items-center justify-center">
                <Shield className="h-8 w-8 text-green-600" />
              </div>
              <div>
                <CardTitle>Access & Permissions</CardTitle>
                <CardDescription>
                  Your current access level and granted permissions.
                </CardDescription>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              <div>
                <div className="flex items-center gap-2 mb-4">
                  <BadgeCheck className="h-5 w-5 text-green-500" />
                  <span className="font-medium">Administrator Access</span>
                  <Badge className="bg-green-100 text-green-800 hover:bg-green-100">Active</Badge>
                </div>
                {permissions.length > 0 ? (
                  <div className="grid gap-2 md:grid-cols-2">
                    {permissions.map((permission) => (
                      <div 
                        key={permission} 
                        className="flex items-center gap-2 p-2 rounded-lg border border-purple-100 bg-purple-50"
                      >
                        <div className="h-2 w-2 rounded-full bg-purple-500" />
                        <span className="text-sm font-medium text-purple-900 capitalize">
                          {permission.split('_').join(' ')}
                        </span>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-sm text-muted-foreground">No permissions assigned.</p>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </SettingsLayout>
  );
} 