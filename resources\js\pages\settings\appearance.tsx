import { Head } from '@inertiajs/react';
import { CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import SettingsLayout from "@/layouts/settings/settings-layout"
import { useAppearance } from "@/hooks/use-appearance"

interface Props {
  role: "client" | "admin" | "superadmin"
}

export default function Appearance({ role }: Props) {
  const { appearance, updateAppearance } = useAppearance()

  return (
    <SettingsLayout role={role}>
      <Head title="Appearance Settings" />

      <CardHeader>
        <CardTitle>Appearance</CardTitle>
        <CardDescription>
          Customize the appearance of the application.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="space-y-4">
          <div>
            <Label>Theme</Label>
            <p className="text-sm text-muted-foreground">
              The application uses a light theme for optimal visibility and accessibility.
            </p>
          </div>

          <div className="flex flex-col items-center justify-between rounded-md border-2 border-muted bg-transparent p-4">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              className="mb-3 h-6 w-6"
            >
              <circle cx="12" cy="12" r="4" />
              <path d="M12 2v2" />
              <path d="M12 20v2" />
              <path d="m4.93 4.93 1.41 1.41" />
              <path d="m17.66 17.66 1.41 1.41" />
              <path d="M2 12h2" />
              <path d="M20 12h2" />
              <path d="m6.34 17.66-1.41 1.41" />
              <path d="m19.07 4.93-1.41 1.41" />
            </svg>
            Light Theme
          </div>
        </div>
      </CardContent>
    </SettingsLayout>
  )
}
