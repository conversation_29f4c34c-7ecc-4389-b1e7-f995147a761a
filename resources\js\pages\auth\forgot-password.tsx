// Components
import { Head, useForm } from '@inertiajs/react';
import { LoaderCircle } from 'lucide-react';
import { FormEventHandler } from 'react';

import InputError from '@/components/input-error';
import TextLink from '@/components/text-link';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import AuthLayout from '@/layouts/auth-layout';

export default function ForgotPassword({ status }: { status?: string }) {
    const { data, setData, post, processing, errors } = useForm<Required<{ email: string }>>({
        email: '',
    });

    const submit: FormEventHandler = (e) => {
        e.preventDefault();

        post(route('password.email'));
    };

    return (
        <AuthLayout title="Forgot password" description="Enter your email to receive a password reset link">
            <Head title="Forgot password" />

            <div className="flex flex-col items-center gap-6">
                {status && <div className="mb-4 text-center text-sm font-medium text-green-600">{status}</div>}

                <form className="flex flex-col gap-6 w-full" onSubmit={submit}>
                    <div className="grid gap-6">
                        <div className="grid gap-2">
                            <Label htmlFor="email" className="text-purple-900">Email address</Label>
                            <Input
                                id="email"
                                type="email"
                                name="email"
                                autoComplete="off"
                                value={data.email}
                                autoFocus
                                tabIndex={1}
                                onChange={(e) => setData('email', e.target.value)}
                                placeholder="<EMAIL>"
                                className="border-purple-300 focus:border-purple-500 focus:ring-purple-500"
                            />
                            <InputError message={errors.email} />
                        </div>

                        <Button 
                            type="submit"
                            className="w-full bg-purple-600 text-white hover:bg-purple-700 font-medium py-2.5 rounded-lg cursor-pointer transition-all duration-300 ease-in-out" 
                            tabIndex={2} 
                            disabled={processing}
                        >
                            {processing && <LoaderCircle className="h-4 w-4 animate-spin" />}
                            Email password reset link
                        </Button>
                    </div>

                    <div className="text-center text-sm text-purple-600">
                        <span>Or, return to </span>
                        <TextLink href={route('login')} tabIndex={3} className="text-purple-800 hover:text-purple-900">
                            log in
                        </TextLink>
                    </div>
                </form>
            </div>
        </AuthLayout>
    );
}
