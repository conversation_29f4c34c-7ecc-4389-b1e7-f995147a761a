import { <PERSON>, CardContent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import AppLayout from '@/layouts/app/app-sidebar-layout';
import { type BreadcrumbItem } from '@/types';
import { Head } from '@inertiajs/react';
import { Users, FileCheck, AlertCircle, Clock, ClipboardList, Calendar, CheckCircle, BarChart2, UserCheck, FileText } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { adminUser } from './tempData/data';

// This would come from your auth context/store in a real app
interface AuthUser {
  id: string;
  name: string;
  email: string;
  permissions: string[];
}

// Example of how we'd get the current user's permissions from auth context
const currentUser: AuthUser = {
  id: "1",
  name: "Admin",
  email: "<EMAIL>",
  permissions: ["case_management", "residency_verification", "service_requests"]
};

interface Application {
    id: string;
    client: string;
    service: string;
    date: string;
    status: 'pending' | 'processing' | 'approved' | 'rejected';
}

interface ScheduleItem {
    id: string;
    client: string;
    service: string;
    time: string;
}

const recentApplications: Application[] = [
    {
        id: '1',
        client: 'John Doe',
        service: 'Medical Assistance',
        date: '2024-04-08',
        status: 'pending'
    },
    {
        id: '2',
        client: 'Jane Smith',
        service: 'Burial Assistance',
        date: '2024-04-07',
        status: 'approved'
    },
    {
        id: '3',
        client: 'Mike Johnson',
        service: 'Educational Aid',
        date: '2024-04-07',
        status: 'processing'
    }
];

const todaySchedule: ScheduleItem[] = [
    {
        id: '1',
        client: 'Sarah Wilson',
        service: 'Initial Interview',
        time: '9:00 AM'
    },
    {
        id: '2',
        client: 'Robert Brown',
        service: 'Document Verification',
        time: '11:30 AM'
    },
    {
        id: '3',
        client: 'Maria Garcia',
        service: 'Follow-up Meeting',
        time: '2:00 PM'
    }
];

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/admin/dashboard',
    },
];

export default function AdminDashboard() {
    // Filter stats based on user permissions
    const getPermissionBasedStats = () => {
        const stats = [];
        
        if (adminUser.permissions.includes('service_requests')) {
            stats.push({
                title: "Total Applications",
                value: "128",
                change: "+14 from last month",
                icon: <FileText className="h-4 w-4 text-purple-400" />
            });
        }
        
        if (adminUser.permissions.includes('residency_verification')) {
            stats.push({
                title: "Pending Review",
                value: "23",
                change: "+7 since yesterday",
                icon: <Clock className="h-4 w-4 text-purple-400" />
            });
        }
        
        if (adminUser.permissions.includes('case_management')) {
            stats.push({
                title: "Active Services",
                value: "12",
                change: "2 need attention",
                icon: <CheckCircle className="h-4 w-4 text-purple-400" />
            });
        }
        
        if (adminUser.permissions.includes('interviews')) {
            stats.push({
                title: "Today's Appointments",
                value: "8",
                change: "3 completed",
                icon: <Calendar className="h-4 w-4 text-purple-400" />
            });
        }
        
        return stats;
    };

    const stats = getPermissionBasedStats();

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Admin Dashboard" />
            
            <div className="flex flex-col gap-6 p-6">
                <div className="flex items-center justify-between">
                    <h1 className="text-2xl font-bold tracking-tight">Welcome, {adminUser.name}</h1>
                </div>

                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                    {stats.map((stat, index) => (
                        <Card key={index}>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">{stat.title}</CardTitle>
                                {stat.icon}
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold">{stat.value}</div>
                                <p className="text-xs text-muted-foreground">{stat.change}</p>
                            </CardContent>
                        </Card>
                    ))}
                </div>

                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
                    {adminUser.permissions.includes('service_requests') && (
                        <Card className="col-span-4">
                            <CardHeader>
                                <CardTitle>Recent Applications</CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="space-y-8">
                                    {recentApplications.map((app) => (
                                        <div key={app.id} className="flex items-center">
                                            <div className="space-y-1">
                                                <p className="text-sm font-medium leading-none">{app.client}</p>
                                                <p className="text-sm text-muted-foreground">
                                                    {app.service} - {app.date}
                                                </p>
                                            </div>
                                            <div className="ml-auto">
                                                {app.status === 'pending' && (
                                                    <Badge variant="outline" className="bg-purple-100 text-purple-800 hover:bg-purple-100">
                                                        Pending Review
                                                    </Badge>
                                                )}
                                                {app.status === 'processing' && (
                                                    <Badge variant="outline" className="bg-blue-100 text-blue-800 hover:bg-blue-100">
                                                        Processing
                                                    </Badge>
                                                )}
                                                {app.status === 'approved' && (
                                                    <Badge variant="outline" className="bg-green-100 text-green-800 hover:bg-green-100">
                                                        Approved
                                                    </Badge>
                                                )}
                                                {app.status === 'rejected' && (
                                                    <Badge variant="outline" className="bg-red-100 text-red-800 hover:bg-red-100">
                                                        Rejected
                                                    </Badge>
                                                )}
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            </CardContent>
                        </Card>
                    )}
                    
                    {adminUser.permissions.includes('interviews') && (
                        <Card className="col-span-3">
                            <CardHeader>
                                <CardTitle>Today's Schedule</CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="space-y-8">
                                    {todaySchedule.map((item) => (
                                        <div key={item.id} className="flex items-center">
                                            <div className="space-y-1">
                                                <p className="text-sm font-medium leading-none">{item.client}</p>
                                                <p className="text-sm text-muted-foreground">{item.service}</p>
                                            </div>
                                            <div className="ml-auto font-medium">
                                                <Badge variant="outline" className="bg-purple-100 text-purple-800 hover:bg-purple-100">
                                                    {item.time}
                                                </Badge>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            </CardContent>
                        </Card>
                    )}
                </div>
            </div>
        </AppLayout>
    );
} 