<?php

use App\Http\Controllers\ProfileController;
use App\Http\Middleware\RoleMiddleware;
use Illuminate\Foundation\Application;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;

// Public Landing Pages
Route::get('/', function () {
    return Inertia::render('welcome');
})->name('home');

Route::get('/services-info', function () {
    return Inertia::render('landing/services-info');
})->name('services-info');

Route::get('/faqs', function () {
    return Inertia::render('landing/faqs');
})->name('faqs');

Route::get('/contact', function () {
    return Inertia::render('landing/contact');
})->name('contact');

// Client Routes
Route::middleware(['auth', 'verified'])->group(function () {
    Route::middleware(RoleMiddleware::class . ':client')->group(function () {
        Route::get('/dashboard', function () {
            return Inertia::render('client/dashboard');
        })->name('dashboard');
        
        Route::get('/residency-verification', function () {
            return Inertia::render('client/residency-verification');
        })->name('residency-verification');
        
        Route::get('/bfaces-application', function () {
            return Inertia::render('client/bfaces-application');
        })->name('bfaces-application');
        
        Route::get('/services', function () {
            return Inertia::render('client/services');
        })->name('services');
        
        Route::get('/applications', function () {
            return Inertia::render('client/applications');
        })->name('applications');
        
        Route::get('/appointments', function () {
            return Inertia::render('client/appointments');
        })->name('appointments');
        
        Route::get('/documents', function () {
            return Inertia::render('client/documents');
        })->name('documents');

        // Client Settings Routes
        Route::prefix('client/settings')->group(function () {
            Route::get('/profile', function () {
                $user = auth()->user();
                $profile = $user->profile;
                
                return Inertia::render('client/settings/profile', [
                    'user' => [
                        'name' => $profile ? $profile->getFullNameAttribute() : $user->name,
                        'email' => $user->email,
                        'phone' => $profile ? $profile->contact_number : null,
                        'barangay' => $profile ? $profile->barangay : null,
                        'address' => $profile ? $profile->getFullAddressAttribute() : null,
                    ],
                ]);
            })->name('client.settings.profile');

            Route::patch('/profile', function (Request $request) {
                $user = auth()->user();
                $profile = $user->profile ?? $user->profile()->create([]);

                $validated = $request->validate([
                    'name' => ['required', 'string', 'max:255'],
                    'email' => ['required', 'string', 'email', 'max:255', Rule::unique('users')->ignore($user->id)],
                    'phone' => ['nullable', 'string', 'max:255'],
                    'barangay' => ['nullable', 'string', 'max:255'],
                    'address' => ['nullable', 'string', 'max:255'],
                ]);

                // Update user email if changed
                if ($validated['email'] !== $user->email) {
                    $user->email = $validated['email'];
                    $user->email_verified_at = null;
                    $user->save();
                }

                // Update profile
                $names = explode(' ', $validated['name']);
                $profile->update([
                    'first_name' => $names[0],
                    'last_name' => end($names),
                    'middle_name' => count($names) > 2 ? implode(' ', array_slice($names, 1, -1)) : null,
                    'contact_number' => $validated['phone'],
                    'barangay' => $validated['barangay'],
                    'street_address' => $validated['address'],
                ]);

                return back();
            })->name('client.settings.profile.update');

            Route::get('/password', function () {
                return Inertia::render('client/settings/password');
            })->name('client.settings.password');

            Route::put('/password', function (Request $request) {
                $validated = $request->validate([
                    'current_password' => ['required', 'current_password'],
                    'password' => ['required', 'confirmed', 'min:8'],
                ]);

                $request->user()->update([
                    'password' => bcrypt($validated['password']),
                ]);

                return back();
            })->name('client.settings.password.update');
        });
    });

    // Admin Routes
    Route::middleware(RoleMiddleware::class . ':admin')->prefix('admin')->group(function () {
        Route::get('/dashboard', function () {
            return Inertia::render('admin/dashboard');
        })->name('admin.dashboard');
        
        Route::get('/verifications', function () {
            return Inertia::render('admin/verifications');
        })->name('admin.verifications');
        
        Route::get('/applications', function () {
            return Inertia::render('admin/applications');
        })->name('admin.applications');
        
        Route::get('/interviews', function () {
            return Inertia::render('admin/interviews');
        })->name('admin.interviews');
        
        Route::get('/reports', function () {
            return Inertia::render('admin/reports');
        })->name('admin.reports');
        
        Route::get('/clients', function () {
            return Inertia::render('admin/clients');
        })->name('admin.clients');
        
        Route::get('/clients/{id}', function (string $id) {
            return Inertia::render('admin/clients/[id]', [
                'params' => [
                    'id' => $id
                ]
            ]);
        })->name('admin.clients.show');
        
        // Admin Settings Routes
        Route::prefix('settings')->group(function () {
            Route::get('/profile', function () {
                $user = auth()->user();
                
                return Inertia::render('admin/settings/profile', [
                    'user' => [
                        'name' => $user->name,
                        'email' => $user->email,
                        'phone' => $user->phone ?? null,
                    ],
                ]);
            })->name('admin.settings.profile');

            Route::patch('/profile', function (Request $request) {
                $user = auth()->user();

                $validated = $request->validate([
                    'name' => ['required', 'string', 'max:255'],
                    'email' => ['required', 'string', 'email', 'max:255', Rule::unique('users')->ignore($user->id)],
                    'phone' => ['nullable', 'string', 'max:255'],
                ]);

                // Update user
                $user->name = $validated['name'];
                if ($validated['email'] !== $user->email) {
                    $user->email = $validated['email'];
                    $user->email_verified_at = null;
                }
                $user->phone = $validated['phone'] ?? null;
                $user->save();

                return back();
            })->name('admin.settings.profile.update');

            Route::get('/password', function () {
                return Inertia::render('admin/settings/password');
            })->name('admin.settings.password');

            Route::put('/password', function (Request $request) {
                $validated = $request->validate([
                    'current_password' => ['required', 'current_password'],
                    'password' => ['required', 'confirmed', 'min:8'],
                ]);

                $request->user()->update([
                    'password' => bcrypt($validated['password']),
                ]);

                return back();
            })->name('admin.settings.password.update');
            
            Route::get('/appearance', function () {
                return Inertia::render('admin/settings/appearance');
            })->name('admin.settings.appearance');
        });
    });

    // Superadmin Routes
    Route::middleware(RoleMiddleware::class . ':superadmin')->prefix('superadmin')->group(function () {
        Route::get('/dashboard', function () {
            return Inertia::render('superadmin/dashboard');
        })->name('superadmin.dashboard');
        
        Route::get('/configuration', function () {
            return Inertia::render('superadmin/configuration');
        })->name('superadmin.configuration');
        
        // Services Management Routes
        Route::prefix('services')->group(function () {
            Route::get('/', function () {
                return Inertia::render('superadmin/services', [
                    'categories' => [],
                    'services' => [],
                    'totalServices' => 0,
                    'activeServices' => 0,
                    'totalCategories' => 0,
                    'activeCategories' => 0,
                ]);
            })->name('superadmin.services');

            Route::get('/new', function () {
                return Inertia::render('superadmin/services/[id]', [
                    'categories' => [], // Add categories data here
                ]);
            })->name('superadmin.services.create');

            Route::get('/{id}/edit', function (string $id) {
                return Inertia::render('superadmin/services/[id]', [
                    'categories' => [], // Add categories data here
                    'service' => [], // Add service data here
                ]);
            })->name('superadmin.services.edit');

            // Service Categories Routes
            Route::prefix('categories')->group(function () {
                Route::get('/new', function () {
                    return Inertia::render('superadmin/services/categories/[id]');
                })->name('superadmin.services.categories.create');

                Route::get('/{id}/edit', function (string $id) {
                    return Inertia::render('superadmin/services/categories/[id]', [
                        'category' => [], // Add category data here
                    ]);
                })->name('superadmin.services.categories.edit');
            });

            // Service Requirements Routes
            Route::prefix('requirements')->group(function () {
                Route::get('/new', function () {
                    return Inertia::render('superadmin/services/requirements/[id]', [
                        'services' => [], // Add services data here
                    ]);
                })->name('superadmin.services.requirements.create');

                Route::get('/{id}/edit', function (string $id) {
                    return Inertia::render('superadmin/services/requirements/[id]', [
                        'services' => [], // Add services data here
                        'requirement' => [], // Add requirement data here
                    ]);
                })->name('superadmin.services.requirements.edit');
            });
        });
        
        Route::get('/users', function () {
            return Inertia::render('superadmin/users');
        })->name('superadmin.users');
        
        Route::get('/budget', function () {
            return Inertia::render('superadmin/budget');
        })->name('superadmin.budget');
        
        Route::get('/reports', function () {
            return Inertia::render('superadmin/reports');
        })->name('superadmin.reports');
        
        Route::get('/database', function () {
            return Inertia::render('superadmin/database');
        })->name('superadmin.database');
        
        // Superadmin Settings Routes
        Route::prefix('settings')->group(function () {
            Route::get('/profile', function () {
                $user = auth()->user();
                
                return Inertia::render('superadmin/settings/profile', [
                    'user' => [
                        'name' => $user->name,
                        'email' => $user->email,
                        'phone' => $user->phone ?? null,
                    ],
                ]);
            })->name('superadmin.settings.profile');

            Route::patch('/profile', function (Request $request) {
                $user = auth()->user();

                $validated = $request->validate([
                    'name' => ['required', 'string', 'max:255'],
                    'email' => ['required', 'string', 'email', 'max:255', Rule::unique('users')->ignore($user->id)],
                    'phone' => ['nullable', 'string', 'max:255'],
                ]);

                // Update user
                $user->name = $validated['name'];
                if ($validated['email'] !== $user->email) {
                    $user->email = $validated['email'];
                    $user->email_verified_at = null;
                }
                $user->phone = $validated['phone'] ?? null;
                $user->save();

                return back();
            })->name('superadmin.settings.profile.update');

            Route::get('/password', function () {
                return Inertia::render('superadmin/settings/password');
            })->name('superadmin.settings.password');

            Route::put('/password', function (Request $request) {
                $validated = $request->validate([
                    'current_password' => ['required', 'current_password'],
                    'password' => ['required', 'confirmed', 'min:8'],
                ]);

                $request->user()->update([
                    'password' => bcrypt($validated['password']),
                ]);

                return back();
            })->name('superadmin.settings.password.update');
            
            Route::get('/appearance', function () {
                return Inertia::render('superadmin/settings/appearance');
            })->name('superadmin.settings.appearance');
        });

        // User Management Routes
        Route::get('/users', function () {
            return Inertia::render('superadmin/users');
        })->name('superadmin.users');

        // Admin Management Routes
        Route::get('/users/admins/new', function () {
            return Inertia::render('superadmin/users/admins/new');
        })->name('superadmin.users.admins.create');

        Route::get('/users/admins/{id}', function (string $id) {
            return Inertia::render('superadmin/users/admins/[id]', [
                'id' => $id
            ]);
        })->name('superadmin.users.admins.edit');

        // Client Management Routes
        Route::get('/users/clients/new', function () {
            return Inertia::render('superadmin/users/clients/new');
        })->name('superadmin.users.clients.create');

        Route::get('/users/clients/{id}', function (string $id) {
            return Inertia::render('superadmin/users/clients/[id]', [
                'id' => $id
            ]);
        })->name('superadmin.users.clients.edit');
    });
});

require __DIR__.'/settings.php';
require __DIR__.'/auth.php';
