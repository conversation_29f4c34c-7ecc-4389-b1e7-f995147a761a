import { Head, useForm } from '@inertiajs/react';
import { LoaderCircle } from 'lucide-react';
import { FormEventHandler, useState } from 'react';

import InputError from '@/components/input-error';
import TextLink from '@/components/text-link';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import AuthLayout from '@/layouts/auth-layout';

type LoginForm = {
    email: string;
    password: string;
    remember: boolean;
    role: string;
};

interface LoginProps {
    status?: string;
    canResetPassword: boolean;
}

export default function Login({ status, canResetPassword }: LoginProps) {
    const [selectedRole, setSelectedRole] = useState('client');
    
    const { data, setData, post, processing, errors, reset } = useForm<Required<LoginForm>>({
        email: '',
        password: '',
        remember: false,
        role: 'client',
    });

    const submit: FormEventHandler = (e) => {
        e.preventDefault();
        
        // Include the selected role in the form data
        setData('role', selectedRole);
        
        post(route('login'), {
            onFinish: () => reset('password'),
        });
    };

    return (
        <AuthLayout 
            title="Log in to Balagtas Social Care" 
            description="Enter your credentials to access the social welfare system"
        >
            <Head title="Log in" />

            <div className="flex flex-col items-center gap-6">
                <form className="flex flex-col gap-6 w-full" onSubmit={submit}>
                    <div className="grid gap-6">
                        <div className="grid gap-2">
                            <Label htmlFor="email" className="text-purple-900">Email address</Label>
                            <Input
                                id="email"
                                type="email"
                                required
                                autoFocus
                                tabIndex={1}
                                autoComplete="email"
                                value={data.email}
                                onChange={(e) => setData('email', e.target.value)}
                                placeholder="<EMAIL>"
                                className="border-purple-300 focus:border-purple-500 focus:ring-purple-500"
                            />
                            <InputError message={errors.email} />
                        </div>

                        <div className="grid gap-2">
                            <div className="flex items-center">
                                <Label htmlFor="password" className="text-purple-900">Password</Label>
                            </div>
                            <Input
                                id="password"
                                type="password"
                                required
                                tabIndex={2}
                                autoComplete="current-password"
                                value={data.password}
                                onChange={(e) => setData('password', e.target.value)}
                                placeholder="Password"
                                className="border-purple-300 focus:border-purple-500 focus:ring-purple-500"
                            />
                            <InputError message={errors.password} />
                            {canResetPassword && (
                                    <TextLink href={route('password.request')} className="ml-auto text-sm text-purple-600 hover:text-purple-800" tabIndex={5}>
                                        Forgot password?
                                    </TextLink>
                                )}
                        </div>

                        <div className="flex items-center space-x-3">
                            <Checkbox
                                id="remember"
                                name="remember"
                                checked={data.remember}
                                onClick={() => setData('remember', !data.remember)}
                                tabIndex={3}
                                className="border-purple-300 text-purple-600 focus:ring-purple-500"
                            />
                            <Label htmlFor="remember" className="text-purple-900">Remember me</Label>
                        </div>

                        <Button 
                            type="submit"
                            className="w-full bg-purple-600 text-white hover:bg-purple-700 font-medium py-2.5 rounded-lg cursor-pointer transition-all duration-300 ease-in-out" 
                            tabIndex={4} 
                            disabled={processing}
                        >
                            {processing && <LoaderCircle className="h-4 w-4" />}
                            Log in
                        </Button>
                    </div>

                    <div className="text-center text-sm text-purple-600">
                        Don't have an account?{' '}
                        <TextLink href={route('register')} tabIndex={5} className="text-purple-800 hover:text-purple-900">
                            Sign up
                        </TextLink>
                    </div>
                </form>

                {status && <div className="mb-4 text-center text-sm font-medium text-green-600">{status}</div>}
            </div>
        </AuthLayout>
    );
}
