<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Profile extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'first_name',
        'middle_name',
        'last_name',
        'suffix',
        'birth_date',
        'gender',
        'contact_number',
        'barangay',
        'street_address',
        'city',
        'province',
        'postal_code',
        'civil_status',
        'occupation',
        'monthly_income',
        'household_members',
        'valid_ids',
        'proof_of_residence',
        'notes'
    ];

    protected $casts = [
        'birth_date' => 'date',
        'monthly_income' => 'decimal:2',
        'valid_ids' => 'array',
        'proof_of_residence' => 'array'
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function getFullNameAttribute()
    {
        return trim(implode(' ', array_filter([
            $this->first_name,
            $this->middle_name,
            $this->last_name,
            $this->suffix
        ])));
    }

    public function getFullAddressAttribute()
    {
        return trim(implode(', ', array_filter([
            $this->street_address,
            $this->barangay,
            $this->city,
            $this->province,
            $this->postal_code
        ])));
    }
}
