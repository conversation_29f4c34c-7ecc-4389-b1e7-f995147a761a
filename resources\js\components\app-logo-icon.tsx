import { SVGAttributes } from 'react';

export default function AppLogoIcon(props: SVGAttributes<SVGElement>) {
    return (
        <div className="flex items-center gap-2">
            <img 
                src="/images/main-logo-white.png" 
                alt="Balagtas Social Care Logo" 
                className="h-10 w-auto"
                onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    // Fallback to the original icon if image fails to load
                    console.error('Logo image failed to load');
                }}
            />
            <div className="grid flex-1 text-left text-sm">
                <span className="mb-0.5 truncate leading-none font-semibold text-white">Balagtas Social Care</span>
                <span className="text-xs text-white">Municipality of Balagtas</span>
            </div>
        </div>
    );
}
