<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Service extends Model
{
    use SoftDeletes;

    protected $fillable = [
        'category_id',
        'name',
        'description',
        'slug',
        'max_amount',
        'processing_days',
        'daily_quota',
        'weekly_quota',
        'monthly_quota',
        'eligibility_criteria',
        'schedule',
        'requires_verification',
        'is_active',
    ];

    protected $casts = [
        'max_amount' => 'decimal:2',
        'processing_days' => 'integer',
        'daily_quota' => 'integer',
        'weekly_quota' => 'integer',
        'monthly_quota' => 'integer',
        'eligibility_criteria' => 'array',
        'schedule' => 'array',
        'requires_verification' => 'boolean',
        'is_active' => 'boolean',
    ];

    /**
     * Get the category that owns the service.
     */
    public function category(): BelongsTo
    {
        return $this->belongsTo(ServiceCategory::class, 'category_id');
    }

    /**
     * Get the requirements for this service.
     */
    public function requirements(): HasMany
    {
        return $this->hasMany(ServiceRequirement::class);
    }

    /**
     * Get the workflow stages for this service.
     */
    public function workflowStages(): HasMany
    {
        return $this->hasMany(ServiceWorkflow::class)->orderBy('order');
    }
}
