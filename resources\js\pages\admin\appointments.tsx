import { Head } from "@inertiajs/react";
import AppLayout from "@/layouts/app/app-sidebar-layout";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Search, User, Calendar, Clock, ExternalLink, CheckCircle, XCircle } from "lucide-react";
import { useState } from "react";
import { type BreadcrumbItem } from "@/types";
import { appointments } from "./tempData/data";
import { type Appointment } from "./tempData/types";
import { format } from "date-fns";

const breadcrumbs: BreadcrumbItem[] = [
  {
    title: "Admin",
    href: "/admin/dashboard",
  },
  {
    title: "Appointments",
    href: "/admin/appointments",
  },
];

export default function Appointments() {
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [purposeFilter, setPurposeFilter] = useState<string>("all");
  const [searchQuery, setSearchQuery] = useState("");
  const [activeTab, setActiveTab] = useState("all");

  const filteredAppointments = appointments.filter((appointment: Appointment) => {
    const matchesStatus =
      statusFilter === "all" || appointment.status === statusFilter;
    const matchesPurpose =
      purposeFilter === "all" || appointment.purpose === purposeFilter;
    const matchesSearch =
      searchQuery === "" ||
      appointment.clientName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      appointment.purpose.toLowerCase().includes(searchQuery.toLowerCase());

    return matchesStatus && matchesPurpose && matchesSearch;
  });

  const uniquePurposes = Array.from(
    new Set(appointments.map((a: Appointment) => a.purpose))
  ).filter((purpose): purpose is string => typeof purpose === 'string');

  const getStatusBadge = (status: Appointment["status"]) => {
    const variants: Record<Appointment["status"], "default" | "secondary" | "destructive" | "outline"> = {
      scheduled: "outline",
      completed: "default",
      cancelled: "destructive",
      "no-show": "destructive"
    };
    const statusDisplay = status.replace('-', ' ');
    return <Badge variant={variants[status]}>{statusDisplay}</Badge>;
  };

  return (
    <AppLayout breadcrumbs={breadcrumbs}>
      <Head title="Appointments" />

      <div className="container py-6">
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-2xl font-bold">Appointments</h1>
            <p className="text-sm text-gray-500 mt-1">
              Manage and track client appointments
            </p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline">
              <Calendar className="h-4 w-4 mr-2" />
              Export Schedule
            </Button>
          </div>
        </div>

        <Card className="p-4 mb-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="flex flex-col gap-2">
              <Label>Search</Label>
              <Input
                placeholder="Search by client or purpose..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
            <div className="flex flex-col gap-2">
              <Label>Status</Label>
              <Select
                value={statusFilter}
                onValueChange={(value) => setStatusFilter(value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Filter by status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All</SelectItem>
                  <SelectItem value="scheduled">Scheduled</SelectItem>
                  <SelectItem value="completed">Completed</SelectItem>
                  <SelectItem value="cancelled">Cancelled</SelectItem>
                  <SelectItem value="no-show">No Show</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="flex flex-col gap-2">
              <Label>Purpose</Label>
              <Select
                value={purposeFilter}
                onValueChange={(value) => setPurposeFilter(value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Filter by purpose" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All</SelectItem>
                  {uniquePurposes.map((purpose) => (
                    <SelectItem key={purpose} value={purpose}>
                      {purpose}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </Card>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="mb-6">
          <TabsList>
            <TabsTrigger value="all">All Appointments</TabsTrigger>
            <TabsTrigger value="scheduled">Scheduled</TabsTrigger>
            <TabsTrigger value="completed">Completed</TabsTrigger>
            <TabsTrigger value="cancelled">Cancelled</TabsTrigger>
            <TabsTrigger value="no-show">No Show</TabsTrigger>
          </TabsList>
        </Tabs>

        <div className="space-y-4">
          {filteredAppointments.map((appointment: Appointment) => (
            <Card key={appointment.id} className="p-4">
              <div className="flex flex-col md:flex-row md:items-start justify-between gap-4">
                <div className="flex flex-col md:flex-row md:items-center gap-4">
                  <div className="space-y-1">
                    <div className="flex flex-wrap gap-2 items-center">
                      <h3 className="font-semibold text-lg">Appointment #{appointment.id}</h3>
                      {getStatusBadge(appointment.status)}
                      <Badge variant="secondary">{appointment.type}</Badge>
                    </div>
                    <p className="text-sm text-gray-500">{appointment.purpose}</p>
                  </div>
                </div>
                
                <div className="flex-1 grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="space-y-1">
                    <div className="text-sm font-medium">Client</div>
                    <div className="flex items-center gap-2">
                      <User className="h-4 w-4 text-gray-400" />
                      <span className="text-sm">{appointment.clientName}</span>
                    </div>
                  </div>
                  
                  <div className="space-y-1">
                    <div className="text-sm font-medium">Schedule</div>
                    <div className="flex items-center gap-1">
                      <Calendar className="h-4 w-4 text-gray-400" />
                      <span className="text-sm">{format(new Date(appointment.date), "PPP")} at {appointment.time}</span>
                    </div>
                  </div>
                  
                  <div className="space-y-1">
                    <div className="text-sm font-medium">Details</div>
                    {appointment.location && (
                      <div className="text-xs text-gray-500">Location: {appointment.location}</div>
                    )}
                    {appointment.documents.length > 0 && (
                      <div className="text-xs text-gray-500">Required Documents: {appointment.documents.join(", ")}</div>
                    )}
                  </div>
                </div>
                
                <div className="flex flex-wrap gap-2 md:self-center">
                  <Button size="sm" variant="outline" asChild>
                    <a href={`/admin/appointments/${appointment.id}`}>
                      <ExternalLink className="h-4 w-4 mr-2" />
                      View
                    </a>
                  </Button>

                  {appointment.status === "scheduled" && (
                    <Button size="sm" variant="default">
                      <CheckCircle className="h-4 w-4 mr-2" />
                      Start
                    </Button>
                  )}
                  
                  {appointment.status === "scheduled" && (
                    <Button size="sm" variant="destructive">
                      <XCircle className="h-4 w-4 mr-2" />
                      Cancel
                    </Button>
                  )}
                </div>
              </div>
              
              {appointment.notes && (
                <div className="mt-3 pt-3 border-t">
                  <div className="text-sm text-gray-700">
                    <span className="font-medium">Notes: </span>
                    {appointment.notes}
                  </div>
                </div>
              )}

              {appointment.recommendation && (
                <div className="mt-3 pt-3 border-t">
                  <div className="text-sm text-gray-700">
                    <span className="font-medium">Recommendation: </span>
                    {appointment.recommendation}
                  </div>
                </div>
              )}
            </Card>
          ))}

          {filteredAppointments.length === 0 && (
            <Card className="p-8">
              <div className="text-center text-gray-500">
                <Calendar className="h-12 w-12 mx-auto mb-4" />
                <h3 className="text-lg font-semibold">No appointments found</h3>
                <p>There are no appointments matching your filters.</p>
              </div>
            </Card>
          )}
        </div>
      </div>
    </AppLayout>
  );
} 