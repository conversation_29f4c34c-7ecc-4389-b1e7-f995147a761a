import React, { useState } from 'react';
import { Menu, X, ArrowRightIcon } from 'lucide-react';
import { Link, usePage } from '@inertiajs/react';
import { type SharedData } from '@/types';

interface HamburgerMenuProps {
    currentPath: string;
    auth: any;
}

export default function HamburgerMenu({ currentPath, auth }: HamburgerMenuProps) {
    const [isOpen, setIsOpen] = useState(false);

    const navItems = [
        { name: 'Home', href: route('home') },
        { name: 'Services', href: route('services-info') },
        { name: 'FAQs', href: route('faqs') },
        { name: 'Contact', href: route('contact') }
    ];

    return (
        <div className="sm:hidden">
            {/* Hamburger Icon */}
            <button
                className="text-gray-700 focus:outline-none hover:text-purple-700 p-2"
                onClick={() => setIsOpen(!isOpen)}
                aria-label="Toggle Menu"
            >
                {isOpen ? <X size={24} /> : <Menu size={24} />}
            </button>

            {/* Mobile Menu Overlay */}
            {isOpen && (
                <>
                    {/* Backdrop */}
                    <div
                        className="fixed inset-0 bg-black bg-opacity-50 z-40"
                        onClick={() => setIsOpen(false)}
                    />

                    {/* Menu Panel */}
                    <div className="fixed top-0 right-0 h-full w-64 bg-white shadow-lg z-50 transform transition-transform duration-300">
                        <div className="p-6">
                            {/* Close Button */}
                            <div className="flex justify-end mb-6">
                                <button
                                    onClick={() => setIsOpen(false)}
                                    className="text-gray-500 hover:text-gray-700"
                                >
                                    <X size={24} />
                                </button>
                            </div>

                            {/* Navigation Links */}
                            <nav className="space-y-4">
                                {navItems.map((item) => (
                                    <Link
                                        key={item.name}
                                        href={item.href}
                                        onClick={() => setIsOpen(false)}
                                        className={`block px-4 py-3 rounded-lg font-medium transition-colors ${
                                            currentPath === item.href
                                                ? "bg-purple-100 text-purple-700"
                                                : "text-gray-700 hover:bg-purple-50 hover:text-purple-700"
                                        }`}
                                    >
                                        {item.name}
                                    </Link>
                                ))}

                                {/* Auth Links */}
                                <div className="border-t border-gray-200 pt-4 mt-6">
                                    {auth.user ? (
                                        <Link
                                            href={route('dashboard')}
                                            onClick={() => setIsOpen(false)}
                                            className="flex items-center gap-2 px-4 py-3 rounded-lg bg-purple-600 text-white font-medium hover:bg-purple-700 transition-colors"
                                        >
                                            Dashboard
                                            <ArrowRightIcon className="h-4 w-4" />
                                        </Link>
                                    ) : (
                                        <div className="space-y-2">
                                            <Link
                                                href={route('login')}
                                                onClick={() => setIsOpen(false)}
                                                className="block px-4 py-3 rounded-lg text-gray-700 hover:bg-purple-50 hover:text-purple-700 font-medium transition-colors"
                                            >
                                                Log in
                                            </Link>
                                            <Link
                                                href={route('register')}
                                                onClick={() => setIsOpen(false)}
                                                className="block px-4 py-3 rounded-lg bg-purple-600 text-white font-medium hover:bg-purple-700 transition-colors text-center"
                                            >
                                                Register
                                            </Link>
                                        </div>
                                    )}
                                </div>
                            </nav>
                        </div>
                    </div>
                </>
            )}
        </div>
    );
}
