import React, { useState } from 'react';
import { Menu, X } from 'lucide-react';

// const navLinks = ['Home', 'Services', 'FAQs' ,'Contact'];

// const navLinks = [
//     { name: 'Home', href: route('home') },
//     { name: 'Services', href: route('services-info') },
//     { name: 'FAQs', href: route('faqs') },
//     { name: 'Contact', href: route('contact') }
// ];

const navLinks = [
    { name: 'Home', href: '/' },
    { name: 'Services', href: '/services-info' },
    { name: 'FAQs', href: '/faqs' },
    { name: 'Contact', href: '/contact' },
];

export default function HamburgerMenu() {
    const [isOpen, setIsOpen] = useState(false);

    return (
        <nav className=" p-4 block sm:hidden">
            <div className="flex justify-between items-center max-w-6xl mx-auto">

                {/* Hamburger Icon */}
                <button 
                    className="md:hidden text-black-700 focus:outline-none hover:text-purple-950"
                    onClick={() => setIsOpen(!isOpen)}
                    aria-label="Toggle Menu"
                >
                    {isOpen ? <X size={24} /> : <Menu size={24} />}
                </button>
            </div>

            {/* Mobile Menu */}
            {isOpen && (
                <ul className="absolute top15 left-0 w-full md:hidden mt-4 space-y-2 text-black font-medium px-2 bg-gray-300 bg-opacity-50 rounded-md">
                    {navLinks.map(link => (
                        <li 
                            key={link.name} 
                            className="hover:bg-purple-500 px-4 py-2 rounded-md cursor-pointer"
                            onClick={() => setIsOpen(false)} // close after click
                        >
                            <a href={link.href} className="block w-full">
                                {link.name}
                            </a>
                        </li>
                    ))}
                </ul>
            )}
        </nav>
    );
}
