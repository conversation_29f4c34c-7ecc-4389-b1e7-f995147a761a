// Components
import { Head, useForm } from '@inertiajs/react';
import { LoaderCircle, Mail } from 'lucide-react';
import { FormEventHandler } from 'react';

import TextLink from '@/components/text-link';
import { Button } from '@/components/ui/button';
import AuthLayout from '@/layouts/auth-layout';

export default function VerifyEmail({ status }: { status?: string }) {
    const { post, processing } = useForm({});

    const submit: FormEventHandler = (e) => {
        e.preventDefault();

        post(route('verification.send'));
    };

    return (
        <AuthLayout title="Verify email" description="Please verify your email address by clicking on the link we just emailed to you.">
            <Head title="Email verification" />

            <div className="flex flex-col items-center gap-6">
                {status === 'verification-link-sent' && (
                    <div className="mb-4 text-center text-sm font-medium text-green-600">
                        A new verification link has been sent to the email address you provided during registration.
                    </div>
                )}

                <form onSubmit={submit} className="flex flex-col gap-6 w-full">
                    <Button 
                        type="submit"
                        className="w-full bg-purple-600 text-white hover:bg-purple-700 font-medium py-2.5 rounded-lg cursor-pointer transition-all duration-300 ease-in-out"
                        disabled={processing}
                    >
                        {processing && <LoaderCircle className="h-4 w-4 animate-spin mr-2" />}
                        Resend verification email
                    </Button>

                    <div className="text-center text-sm text-purple-600">
                        <TextLink href={route('logout')} method="post" className="text-purple-700 hover:text-purple-800 font-medium">
                            Log out
                        </TextLink>
                    </div>
                </form>
            </div>
        </AuthLayout>
    );
}
