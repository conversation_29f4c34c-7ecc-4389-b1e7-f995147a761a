import { Head } from "@inertiajs/react";
import AppLayout from "@/layouts/app/app-sidebar-layout";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { type BreadcrumbItem } from "@/types";
import { 
  User, 
  Mail, 
  Phone, 
  Home, 
  Calendar, 
  FileText, 
  Clock, 
  CheckCircle, 
  AlertCircle,
  Edit,
  History,
  MessageSquare,
  Bookmark,
  ClipboardList,
  MapPin
} from "lucide-react";
import { useState } from "react";

interface ClientDetail {
  id: string;
  name: string;
  email: string;
  phone: string;
  barangay: string;
  address: string;
  status: "verified" | "pending" | "inactive" | "blocked";
  joinedDate: string;
  lastActive: string;
  birthDate: string;
  gender: "male" | "female" | "other";
  civilStatus: "single" | "married" | "widowed" | "separated";
  occupation: string;
  bfacesStatus: "verified" | "pending" | "none";
  notes: string[];
}

interface Application {
  id: string;
  service: string;
  status: "pending" | "approved" | "rejected" | "completed";
  appliedDate: string;
  completedDate?: string;
  notes?: string;
}

interface Document {
  id: string;
  name: string;
  type: string;
  uploadDate: string;
  status: "verified" | "pending" | "rejected";
  fileUrl: string;
}

interface Interaction {
  id: string;
  type: "interview" | "call" | "visit" | "email" | "message";
  date: string;
  notes: string;
  conductor: string;
}

// Dummy data for the client
const dummyClient: ClientDetail = {
  id: "1",
  name: "Juan Dela Cruz",
  email: "<EMAIL>",
  phone: "09123456789",
  barangay: "San Miguel",
  address: "123 Main St., San Miguel, Balagtas",
  status: "verified",
  joinedDate: "2024-01-15",
  lastActive: "2024-04-08",
  birthDate: "1985-06-15",
  gender: "male",
  civilStatus: "married",
  occupation: "Farmer",
  bfacesStatus: "verified",
  notes: [
    "Client has 3 children aged 5, 8, and 12",
    "Primary income from farming rice fields",
    "Spouse works part-time as a seamstress"
  ]
};

// Dummy applications
const dummyApplications: Application[] = [
  {
    id: "app1",
    service: "Medical Assistance",
    status: "completed",
    appliedDate: "2024-02-10",
    completedDate: "2024-02-15",
    notes: "Received PHP 5,000 for medical expenses"
  },
  {
    id: "app2",
    service: "Educational Aid",
    status: "approved",
    appliedDate: "2024-03-20",
    notes: "For eldest child's school supplies"
  },
  {
    id: "app3",
    service: "Senior Citizen Support",
    status: "pending",
    appliedDate: "2024-04-05",
    notes: "For client's mother"
  }
];

// Dummy documents
const dummyDocuments: Document[] = [
  {
    id: "doc1",
    name: "Barangay Residency Certificate",
    type: "Proof of Residency",
    uploadDate: "2024-01-15",
    status: "verified",
    fileUrl: "/documents/residency-1.pdf"
  },
  {
    id: "doc2",
    name: "Birth Certificate",
    type: "Personal Identification",
    uploadDate: "2024-01-15",
    status: "verified",
    fileUrl: "/documents/birth-1.pdf"
  },
  {
    id: "doc3",
    name: "Medical Certificate",
    type: "Medical Document",
    uploadDate: "2024-02-10",
    status: "verified",
    fileUrl: "/documents/medical-1.pdf"
  },
  {
    id: "doc4",
    name: "Income Certificate",
    type: "Financial Document",
    uploadDate: "2024-03-20",
    status: "pending",
    fileUrl: "/documents/income-1.pdf"
  }
];

// Dummy interactions
const dummyInteractions: Interaction[] = [
  {
    id: "int1",
    type: "interview",
    date: "2024-01-20",
    notes: "Initial assessment interview. Client explained family situation and needs.",
    conductor: "Maria Reyes"
  },
  {
    id: "int2",
    type: "call",
    date: "2024-02-05",
    notes: "Follow-up call about medical assistance requirements.",
    conductor: "Pedro Santos"
  },
  {
    id: "int3",
    type: "visit",
    date: "2024-03-01",
    notes: "Home visit to verify living conditions. Confirmed 5 family members in a small 2-bedroom home.",
    conductor: "Maria Reyes"
  },
  {
    id: "int4",
    type: "interview",
    date: "2024-03-15",
    notes: "Interview for educational aid application. Discussed children's schooling needs.",
    conductor: "Pedro Santos"
  }
];

export default function ClientDetail({ params }: { params: { id: string } }) {
  const clientId = params.id;
  const [activeTab, setActiveTab] = useState("profile");
  
  // In a real application, you would fetch the client data based on the ID
  // Here we're just using dummy data
  const client = dummyClient;
  
  const breadcrumbs: BreadcrumbItem[] = [
    {
      title: "Admin",
      href: "/admin/dashboard",
    },
    {
      title: "Clients",
      href: "/admin/clients",
    },
    {
      title: client.name,
      href: `/admin/clients/${clientId}`,
    },
  ];

  const getStatusBadge = (status: ClientDetail["status"]) => {
    const variants: Record<ClientDetail["status"], "default" | "secondary" | "destructive" | "outline"> = {
      verified: "secondary",
      pending: "default",
      inactive: "outline",
      blocked: "destructive",
    };
    return <Badge variant={variants[status]}>{status}</Badge>;
  };

  const getApplicationStatusBadge = (status: Application["status"]) => {
    const variants: Record<Application["status"], "default" | "secondary" | "destructive" | "outline"> = {
      pending: "outline",
      approved: "default",
      rejected: "destructive",
      completed: "secondary",
    };
    return <Badge variant={variants[status]}>{status}</Badge>;
  };

  const getDocumentStatusBadge = (status: Document["status"]) => {
    const variants: Record<Document["status"], "default" | "secondary" | "destructive" | "outline"> = {
      verified: "secondary",
      pending: "default",
      rejected: "destructive",
    };
    return <Badge variant={variants[status]}>{status}</Badge>;
  };

  return (
    <AppLayout breadcrumbs={breadcrumbs}>
      <Head title={`Client: ${client.name}`} />

      <div className="container py-6">
        <div className="flex justify-between items-center mb-6">
          <div className="flex items-center gap-4">
            <div className="bg-gray-100 p-3 rounded-full">
              <User className="h-8 w-8 text-gray-600" />
            </div>
            <div>
              <div className="flex items-center gap-2">
                <h1 className="text-2xl font-bold">{client.name}</h1>
                {getStatusBadge(client.status)}
                <Badge variant={client.bfacesStatus === "verified" ? "secondary" : "outline"}>
                  BFACES: {client.bfacesStatus}
                </Badge>
              </div>
              <p className="text-sm text-gray-500">Client ID: {client.id}</p>
            </div>
          </div>
          <div className="flex gap-2">
            <Button variant="outline">
              <History className="h-4 w-4 mr-2" />
              Activity Log
            </Button>
            <Button variant="outline">
              <MessageSquare className="h-4 w-4 mr-2" />
              Add Note
            </Button>
            <Button>
              <Edit className="h-4 w-4 mr-2" />
              Edit Profile
            </Button>
          </div>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="mb-6">
          <TabsList className="grid grid-cols-2 md:grid-cols-4 w-full">
            <TabsTrigger value="profile">Profile</TabsTrigger>
            <TabsTrigger value="applications">Applications</TabsTrigger>
            <TabsTrigger value="documents">Documents</TabsTrigger>
            <TabsTrigger value="interactions">Interactions</TabsTrigger>
          </TabsList>

          <TabsContent value="profile" className="mt-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <Card className="md:col-span-2">
                <CardHeader>
                  <CardTitle>Personal Information</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <h3 className="text-sm font-medium text-gray-500">Full Name</h3>
                      <p>{client.name}</p>
                    </div>
                    <div className="space-y-2">
                      <h3 className="text-sm font-medium text-gray-500">Email</h3>
                      <p className="flex items-center gap-2">
                        <Mail className="h-4 w-4 text-gray-400" />
                        {client.email}
                      </p>
                    </div>
                    <div className="space-y-2">
                      <h3 className="text-sm font-medium text-gray-500">Phone</h3>
                      <p className="flex items-center gap-2">
                        <Phone className="h-4 w-4 text-gray-400" />
                        {client.phone}
                      </p>
                    </div>
                    <div className="space-y-2">
                      <h3 className="text-sm font-medium text-gray-500">Date of Birth</h3>
                      <p className="flex items-center gap-2">
                        <Calendar className="h-4 w-4 text-gray-400" />
                        {client.birthDate}
                      </p>
                    </div>
                    <div className="space-y-2">
                      <h3 className="text-sm font-medium text-gray-500">Gender</h3>
                      <p className="capitalize">{client.gender}</p>
                    </div>
                    <div className="space-y-2">
                      <h3 className="text-sm font-medium text-gray-500">Civil Status</h3>
                      <p className="capitalize">{client.civilStatus}</p>
                    </div>
                    <div className="space-y-2">
                      <h3 className="text-sm font-medium text-gray-500">Occupation</h3>
                      <p>{client.occupation}</p>
                    </div>
                    <div className="space-y-2">
                      <h3 className="text-sm font-medium text-gray-500">Member Since</h3>
                      <p className="flex items-center gap-2">
                        <Clock className="h-4 w-4 text-gray-400" />
                        {client.joinedDate}
                      </p>
                    </div>
                  </div>

                  <div className="mt-6">
                    <h3 className="text-sm font-medium text-gray-500 mb-2">Address</h3>
                    <div className="flex items-start gap-2">
                      <MapPin className="h-4 w-4 text-gray-400 mt-0.5" />
                      <div>
                        <p>{client.address}</p>
                        <p className="text-sm text-gray-500">Barangay: {client.barangay}</p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <div className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Status Information</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="flex justify-between items-center">
                        <span className="text-sm">Account Status</span>
                        {getStatusBadge(client.status)}
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm">BFACES Status</span>
                        <Badge variant={client.bfacesStatus === "verified" ? "secondary" : "outline"}>
                          {client.bfacesStatus}
                        </Badge>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm">Last Active</span>
                        <span className="text-sm">{client.lastActive}</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Notes</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      {client.notes.map((note, index) => (
                        <div key={index} className="text-sm p-2 bg-gray-50 rounded">
                          {note}
                        </div>
                      ))}
                      <Button variant="outline" className="w-full mt-2">
                        <Bookmark className="h-4 w-4 mr-2" />
                        Add Note
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="applications" className="mt-6">
            <Card>
              <CardHeader>
                <div className="flex justify-between items-center">
                  <CardTitle>Application History</CardTitle>
                  <Button>
                    <ClipboardList className="h-4 w-4 mr-2" />
                    New Application
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                {dummyApplications.length > 0 ? (
                  <div className="space-y-4">
                    {dummyApplications.map((app) => (
                      <div key={app.id} className="p-4 border rounded-md">
                        <div className="flex justify-between items-start">
                          <div>
                            <div className="flex items-center gap-2 mb-2">
                              <h3 className="font-medium">{app.service}</h3>
                              {getApplicationStatusBadge(app.status)}
                            </div>
                            <div className="text-sm text-gray-500">
                              <div className="flex items-center gap-1">
                                <Calendar className="h-4 w-4" />
                                Applied: {app.appliedDate}
                              </div>
                              {app.completedDate && (
                                <div className="flex items-center gap-1">
                                  <CheckCircle className="h-4 w-4" />
                                  Completed: {app.completedDate}
                                </div>
                              )}
                              {app.notes && (
                                <div className="mt-2 p-2 bg-gray-50 rounded">
                                  {app.notes}
                                </div>
                              )}
                            </div>
                          </div>
                          <Button variant="outline" size="sm">
                            View Details
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center p-6 text-gray-500">
                    <ClipboardList className="h-12 w-12 mx-auto mb-4" />
                    <h3 className="text-lg font-semibold">No applications yet</h3>
                    <p>Client hasn't applied for any services.</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="documents" className="mt-6">
            <Card>
              <CardHeader>
                <div className="flex justify-between items-center">
                  <CardTitle>Documents</CardTitle>
                  <Button>
                    <FileText className="h-4 w-4 mr-2" />
                    Upload Document
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                {dummyDocuments.length > 0 ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {dummyDocuments.map((doc) => (
                      <div key={doc.id} className="p-4 border rounded-md">
                        <div className="flex justify-between items-start">
                          <div>
                            <div className="flex items-center gap-2 mb-1">
                              <h3 className="font-medium">{doc.name}</h3>
                              {getDocumentStatusBadge(doc.status)}
                            </div>
                            <p className="text-sm text-gray-500">{doc.type}</p>
                            <div className="text-sm text-gray-500 mt-1">
                              <div className="flex items-center gap-1">
                                <Calendar className="h-4 w-4" />
                                Uploaded: {doc.uploadDate}
                              </div>
                            </div>
                          </div>
                          <Button variant="outline" size="sm" asChild>
                            <a href={doc.fileUrl} target="_blank" rel="noopener noreferrer">
                              View
                            </a>
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center p-6 text-gray-500">
                    <FileText className="h-12 w-12 mx-auto mb-4" />
                    <h3 className="text-lg font-semibold">No documents yet</h3>
                    <p>Client hasn't uploaded any documents.</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="interactions" className="mt-6">
            <Card>
              <CardHeader>
                <div className="flex justify-between items-center">
                  <CardTitle>Interaction History</CardTitle>
                  <Button>
                    <MessageSquare className="h-4 w-4 mr-2" />
                    Log Interaction
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                {dummyInteractions.length > 0 ? (
                  <div className="space-y-4">
                    {dummyInteractions.map((interaction) => (
                      <div key={interaction.id} className="p-4 border rounded-md">
                        <div className="flex items-start justify-between">
                          <div>
                            <div className="flex items-center gap-2 mb-2">
                              <h3 className="font-medium capitalize">{interaction.type}</h3>
                              <span className="text-sm text-gray-500">
                                by {interaction.conductor}
                              </span>
                            </div>
                            <div className="text-sm text-gray-500">
                              <div className="flex items-center gap-1 mb-2">
                                <Calendar className="h-4 w-4" />
                                Date: {interaction.date}
                              </div>
                              <div className="p-2 bg-gray-50 rounded">
                                {interaction.notes}
                              </div>
                            </div>
                          </div>
                          <Button variant="outline" size="sm">
                            Edit
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center p-6 text-gray-500">
                    <MessageSquare className="h-12 w-12 mx-auto mb-4" />
                    <h3 className="text-lg font-semibold">No interactions yet</h3>
                    <p>No interaction records found for this client.</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </AppLayout>
  );
} 