import { type ReactNode } from "react"
import AppLayout from "@/layouts/app-layout"
import { Card } from "@/components/ui/card"
import { cn } from "@/lib/utils"
import { Link } from "@inertiajs/react"
import { type BreadcrumbItem } from "@/types"

interface SettingsLayoutProps {
  children: ReactNode
  role: "client" | "admin" | "superadmin"
}

export default function SettingsLayout({ children, role }: SettingsLayoutProps) {
  const breadcrumbs: BreadcrumbItem[] = [
    { title: "Dashboard", href: `/${role}/dashboard` },
    { title: "Settings", href: `/${role}/settings/profile` },
  ]

  const tabs = [
    {
      title: "Profile",
      href: `/${role}/settings/profile`,
    },
    {
      title: "Password",
      href: `/${role}/settings/password`,
    },
  ]

  return (
    <AppLayout breadcrumbs={breadcrumbs}>
      <div className="space-y-6">
        <div className="flex flex-col space-y-8 lg:flex-row lg:space-x-12 lg:space-y-0">
          <aside className="-mx-4 lg:w-1/5">
            <nav className="flex space-x-2 lg:flex-col lg:space-x-0 lg:space-y-1">
              {tabs.map((tab) => (
                <Link
                  key={tab.href}
                  href={tab.href}
                  className={cn(
                    "justify-start px-4 py-2 text-sm font-medium text-muted-foreground hover:bg-muted hover:text-primary",
                    window.location.pathname === tab.href &&
                      "bg-muted font-medium text-primary"
                  )}
                >
                  {tab.title}
                </Link>
              ))}
            </nav>
          </aside>
          <div className="flex-1">
            <div className="h-full space-y-6">
              <Card className="p-6">{children}</Card>
            </div>
          </div>
        </div>
      </div>
    </AppLayout>
  )
} 