import { Head } from "@inertiajs/react"
import AppLayout from "@/layouts/app-layout"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { type BreadcrumbItem } from "@/types"
import Heading from "@/components/heading"
import { Badge } from "@/components/ui/badge"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { AlertCircle, Calendar, FileText, Loader2, Mail, MapPin, Phone, User2 } from "lucide-react"
import { useForm } from "@inertiajs/react"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"

interface Case {
  id: number
  client: {
    id: number
    name: string
    email: string
    phone?: string
    barangay?: string
    address?: string
  }
  type: "bfaces" | "service" | "general"
  status: "new" | "in_progress" | "pending_review" | "resolved" | "closed"
  priority: "low" | "medium" | "high" | "urgent"
  assignedAt: string
  lastUpdated: string
  nextFollowUp?: string
  description: string
  notes: {
    id: number
    content: string
    createdAt: string
    createdBy: {
      id: number
      name: string
    }
  }[]
  documents?: {
    id: number
    name: string
    type: string
    uploadedAt: string
    url: string
  }[]
  timeline: {
    id: number
    type: "status_change" | "note_added" | "document_uploaded" | "follow_up_scheduled"
    description: string
    createdAt: string
    createdBy: {
      id: number
      name: string
    }
  }[]
}

interface Props {
  case: Case
  error?: string
}

export default function CaseDetails({ case: case_, error }: Props) {
  const breadcrumbs: BreadcrumbItem[] = [
    { title: "Dashboard", href: "/admin/dashboard" },
    { title: "Case Management", href: "/admin/case-management" },
    { title: `Case #${case_.id}`, href: `/admin/case-management/${case_.id}` },
  ]

  const { data, setData, post, processing, errors } = useForm({
    note: "",
    status: case_.status,
    priority: case_.priority,
    nextFollowUp: case_.nextFollowUp || "",
  })

  function getStatusColor(status: Case["status"]) {
    switch (status) {
      case "new":
        return "bg-blue-100 text-blue-800"
      case "in_progress":
        return "bg-yellow-100 text-yellow-800"
      case "pending_review":
        return "bg-purple-100 text-purple-800"
      case "resolved":
        return "bg-green-100 text-green-800"
      case "closed":
        return "bg-gray-100 text-gray-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  function getPriorityColor(priority: Case["priority"]) {
    switch (priority) {
      case "urgent":
        return "bg-red-100 text-red-800"
      case "high":
        return "bg-orange-100 text-orange-800"
      case "medium":
        return "bg-yellow-100 text-yellow-800"
      case "low":
        return "bg-green-100 text-green-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  function addNote(e: React.FormEvent) {
    e.preventDefault()
    post(`/admin/case-management/${case_.id}/notes`, {
      onSuccess: () => {
        setData("note", "")
      },
    })
  }

  function updateCase(e: React.FormEvent) {
    e.preventDefault()
    post(`/admin/case-management/${case_.id}`)
  }

  return (
    <AppLayout breadcrumbs={breadcrumbs}>
      <Head title={`Case #${case_.id}`} />

      <div className="flex flex-col gap-6">
        <div className="flex items-center justify-between">
          <Heading 
            title={`Case #${case_.id}`}
            description="View and manage case details." 
          />
          <div className="flex gap-2">
            <Badge className={getPriorityColor(case_.priority)}>
              {case_.priority.charAt(0).toUpperCase() + case_.priority.slice(1)}
            </Badge>
            <Badge className={getStatusColor(case_.status)}>
              {case_.status.split("_").map(word => 
                word.charAt(0).toUpperCase() + word.slice(1)
              ).join(" ")}
            </Badge>
          </div>
        </div>

        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        <div className="grid gap-6 md:grid-cols-3">
          {/* Client Information */}
          <Card className="md:col-span-1">
            <CardHeader>
              <CardTitle>Client Information</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center gap-2">
                  <User2 className="h-4 w-4 text-muted-foreground" />
                  <span className="font-medium">{case_.client.name}</span>
                </div>
                {case_.client.email && (
                  <div className="flex items-center gap-2">
                    <Mail className="h-4 w-4 text-muted-foreground" />
                    <a
                      href={`mailto:${case_.client.email}`}
                      className="text-sm text-muted-foreground hover:underline"
                    >
                      {case_.client.email}
                    </a>
                  </div>
                )}
                {case_.client.phone && (
                  <div className="flex items-center gap-2">
                    <Phone className="h-4 w-4 text-muted-foreground" />
                    <a
                      href={`tel:${case_.client.phone}`}
                      className="text-sm text-muted-foreground hover:underline"
                    >
                      {case_.client.phone}
                    </a>
                  </div>
                )}
                {(case_.client.barangay || case_.client.address) && (
                  <div className="flex items-start gap-2">
                    <MapPin className="h-4 w-4 text-muted-foreground" />
                    <div className="text-sm text-muted-foreground">
                      {case_.client.barangay && <div>Barangay {case_.client.barangay}</div>}
                      {case_.client.address && <div>{case_.client.address}</div>}
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Case Details */}
          <Card className="md:col-span-2">
            <CardHeader>
              <CardTitle>Case Details</CardTitle>
            </CardHeader>
            <CardContent>
              <form onSubmit={updateCase} className="space-y-4">
                <div className="grid gap-4 md:grid-cols-2">
                  <div className="space-y-2">
                    <Label htmlFor="status">Status</Label>
                    <select
                      id="status"
                      className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                      value={data.status}
                      onChange={e => setData("status", e.target.value as Case["status"])}
                    >
                      <option value="new">New</option>
                      <option value="in_progress">In Progress</option>
                      <option value="pending_review">Pending Review</option>
                      <option value="resolved">Resolved</option>
                      <option value="closed">Closed</option>
                    </select>
                    {errors.status && (
                      <p className="text-sm text-red-500">{errors.status}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="priority">Priority</Label>
                    <select
                      id="priority"
                      className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                      value={data.priority}
                      onChange={e => setData("priority", e.target.value as Case["priority"])}
                    >
                      <option value="urgent">Urgent</option>
                      <option value="high">High</option>
                      <option value="medium">Medium</option>
                      <option value="low">Low</option>
                    </select>
                    {errors.priority && (
                      <p className="text-sm text-red-500">{errors.priority}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="nextFollowUp">Next Follow-up</Label>
                    <input
                      id="nextFollowUp"
                      type="date"
                      className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                      value={data.nextFollowUp}
                      onChange={e => setData("nextFollowUp", e.target.value)}
                      min={new Date().toISOString().split("T")[0]}
                    />
                    {errors.nextFollowUp && (
                      <p className="text-sm text-red-500">{errors.nextFollowUp}</p>
                    )}
                  </div>
                </div>

                <div className="flex justify-end">
                  <Button type="submit" disabled={processing}>
                    {processing ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Updating...
                      </>
                    ) : (
                      "Update Case"
                    )}
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>

          {/* Documents */}
          <Card className="md:col-span-1">
            <CardHeader>
              <CardTitle>Documents</CardTitle>
            </CardHeader>
            <CardContent>
              {case_.documents && case_.documents.length > 0 ? (
                <div className="space-y-4">
                  {case_.documents.map(doc => (
                    <div key={doc.id} className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <FileText className="h-4 w-4 text-muted-foreground" />
                        <div>
                          <p className="text-sm font-medium">{doc.name}</p>
                          <p className="text-xs text-muted-foreground">
                            Uploaded on {new Date(doc.uploadedAt).toLocaleDateString()}
                          </p>
                        </div>
                      </div>
                      <Button variant="outline" size="sm" asChild>
                        <a href={doc.url} target="_blank" rel="noopener noreferrer">
                          View
                        </a>
                      </Button>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-sm text-muted-foreground">No documents uploaded yet.</p>
              )}
            </CardContent>
          </Card>

          {/* Timeline */}
          <Card className="md:col-span-2">
            <CardHeader>
              <CardTitle>Timeline</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {case_.timeline.map(event => (
                  <div key={event.id} className="flex gap-4">
                    <div className="relative flex items-center">
                      <div className="h-2 w-2 rounded-full bg-muted-foreground" />
                      <div className="absolute h-full w-px bg-border" />
                    </div>
                    <div className="flex-1 space-y-1">
                      <p className="text-sm">{event.description}</p>
                      <div className="flex items-center gap-2 text-xs text-muted-foreground">
                        <span>{event.createdBy.name}</span>
                        <span>•</span>
                        <span>{new Date(event.createdAt).toLocaleString()}</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Notes */}
          <Card className="md:col-span-3">
            <CardHeader>
              <CardTitle>Case Notes</CardTitle>
              <CardDescription>
                Add notes and updates about the case.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={addNote} className="mb-6 space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="note">Add Note</Label>
                  <Textarea
                    id="note"
                    placeholder="Enter your note here..."
                    value={data.note}
                    onChange={e => setData("note", e.target.value)}
                    rows={4}
                  />
                  {errors.note && (
                    <p className="text-sm text-red-500">{errors.note}</p>
                  )}
                </div>

                <div className="flex justify-end">
                  <Button type="submit" disabled={processing}>
                    {processing ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Adding Note...
                      </>
                    ) : (
                      "Add Note"
                    )}
                  </Button>
                </div>
              </form>

              <div className="space-y-4">
                {case_.notes.map(note => (
                  <div key={note.id} className="rounded-lg border p-4">
                    <p className="text-sm">{note.content}</p>
                    <div className="mt-2 flex items-center gap-2 text-xs text-muted-foreground">
                      <span>{note.createdBy.name}</span>
                      <span>•</span>
                      <span>{new Date(note.createdAt).toLocaleString()}</span>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </AppLayout>
  )
} 