import { Head, useForm } from "@inertiajs/react"
import AppLayout from "@/layouts/app-layout"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { type BreadcrumbItem } from "@/types"
import Heading from "@/components/heading"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import { useState } from "react"

interface Category {
  id: number
  name: string
  description: string | null
  slug: string
  icon: string | null
  is_active: boolean
  services_count: number
}

interface Props {
  category?: Category
}

export default function ServiceCategoryForm({ category }: Props) {
  const isEditing = !!category
  const { data, setData, post, put, processing, errors } = useForm({
    name: category?.name ?? "",
    description: category?.description ?? "",
    icon: category?.icon ?? "",
    is_active: category?.is_active ?? true,
  })

  const breadcrumbs: BreadcrumbItem[] = [
    { title: "Dashboard", href: "/superadmin/dashboard" },
    { title: "Services Configuration", href: "/superadmin/services" },
    { 
      title: isEditing ? "Edit Category" : "New Category", 
      href: isEditing ? `/superadmin/services/categories/${category.id}` : "/superadmin/services/categories/new"
    },
  ]

  function handleSubmit(e: React.FormEvent) {
    e.preventDefault()
    if (isEditing) {
      put(`/superadmin/services/categories/${category.id}`)
    } else {
      post("/superadmin/services/categories")
    }
  }

  return (
    <AppLayout breadcrumbs={breadcrumbs}>
      <Head title={isEditing ? "Edit Service Category" : "New Service Category"} />

      <div className="flex flex-col gap-6">
        <div className="flex items-center justify-between">
          <Heading 
            title={isEditing ? "Edit Service Category" : "New Service Category"}
            description={
              isEditing 
                ? "Update an existing service category."
                : "Create a new service category."
            }
          />
        </div>

        <form onSubmit={handleSubmit}>
          <Card>
            <CardHeader>
              <CardTitle>Category Details</CardTitle>
              <CardDescription>
                Basic information about the service category.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="name">Name</Label>
                <Input
                  id="name"
                  value={data.name}
                  onChange={e => setData("name", e.target.value)}
                  placeholder="Enter category name"
                />
                {errors.name && (
                  <p className="text-sm text-red-500">{errors.name}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  value={data.description}
                  onChange={e => setData("description", e.target.value)}
                  placeholder="Enter category description"
                />
                {errors.description && (
                  <p className="text-sm text-red-500">{errors.description}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="icon">Icon</Label>
                <Input
                  id="icon"
                  value={data.icon}
                  onChange={e => setData("icon", e.target.value)}
                  placeholder="Enter icon class or URL"
                />
                {errors.icon && (
                  <p className="text-sm text-red-500">{errors.icon}</p>
                )}
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="is_active"
                  checked={data.is_active}
                  onCheckedChange={checked => setData("is_active", checked)}
                />
                <Label htmlFor="is_active">Active</Label>
              </div>

              <div className="flex justify-end space-x-2">
                <Button variant="outline" type="button" asChild>
                  <a href="/superadmin/services">Cancel</a>
                </Button>
                <Button type="submit" disabled={processing}>
                  {isEditing ? "Update Category" : "Create Category"}
                </Button>
              </div>
            </CardContent>
          </Card>
        </form>
      </div>
    </AppLayout>
  )
} 